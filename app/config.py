import os
from urllib.parse import urlparse

from cryptography.fernet import Fernet
from dotenv import load_dotenv
from flask import current_app

from app.constants import ErrorCode
from app.utils.errors import APIError

load_dotenv()  # 加载 .env 文件（本地开发）


class Config:
    """基础配置"""
    LOG_TO_FILE = False

    SQLALCHEMY_DATABASE_URI = os.getenv('SQLALCHEMY_DATABASE_URI')  # 保密！
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        # 防止连接超时被回收
        "pool_recycle": 3000,  # 每小时回收连接（小于MySQL的wait_timeout）
        "pool_pre_ping": True,  # 执行前健康检查
        "pool_size": 20,  # 连接池大小
        "max_overflow": 10  # 允许临时超出连接数
    }

    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', '')  # 保密！
    REDIS_MAX_CONNECTIONS = int(os.getenv('REDIS_MAX_CONNECTIONS', 20))

    KOMBU_BROKER_URL = os.getenv('KOMBU_BROKER_URL', 'amqp://guest:guest@localhost:5672//')

    ENCRYPT_KEY = os.getenv('ENCRYPT_KEY')  # 保密！
    EXPOSE_ERROR_DETAILS = os.getenv('EXPOSE_ERROR_DETAILS', 'False').lower() in ('true', '1', 't')

    WECHAT_APPID = os.getenv('WECHAT_APPID')
    WECHAT_SECRET = os.getenv('WECHAT_SECRET')  # 保密！
    MCH_ID = os.getenv('MCH_ID')
    CERT_SERIAL_NO = os.getenv('CERT_SERIAL_NO')
    APIV3_KEY = os.getenv('APIV3_KEY')  # 保密！
    API_BASE_URL = os.getenv('API_BASE_URL', 'znyx.xiaochaitec.cn')
    NOTIFY_URL = os.getenv('NOTIFY_URL', 'https://znyx.xiaochaitec.cn/notify/wechatpay')
    REFUND_NOTIFY_URL = os.getenv('REFUND_NOTIFY_URL', 'https://znyx.xiaochaitec.cn/notify/wechatpay/refund')
    TRANSFER_NOTIFY_URL = os.getenv('TRANSFER_NOTIFY_URL', 'https://znyx.xiaochaitec.cn/notify/wechatpay/transfer')
    PUBLIC_KEY_ID = os.getenv('PUBLIC_KEY_ID')

    # 图片上传配置
    IMAGE_TEMP_DIR = os.getenv('IMAGE_TEMP_DIR', os.path.join(os.getcwd(), 'temp_images'))
    IMAGE_STORAGE_DIR = os.getenv('IMAGE_STORAGE_DIR', os.path.join(os.getcwd(), 'images'))
    MAX_IMAGE_SIZE = int(os.getenv('MAX_IMAGE_SIZE', 2 * 1024 * 1024))  # 默认2MB
    TEMP_IMAGE_EXPIRY = int(os.getenv('TEMP_IMAGE_EXPIRY', 5 * 60))  # 默认5分钟过期
    MAX_TEMP_IMAGE_EXPIRY = int(os.getenv('MAX_TEMP_IMAGE_EXPIRY', 24 * 60 * 60))  # 最大允许1天
    ALLOWED_IMAGE_EXTENSIONS = set(os.getenv('ALLOWED_IMAGE_EXTENSIONS', 'png,jpg,jpeg').split(','))
    IMAGE_BASE_URL = os.getenv('IMAGE_BASE_URL', 'https://static.znyx.xiaochaitec.cn/images/bucket')

    # 微信支付账单存储配置
    BILL_STORAGE_DIR = os.getenv('BILL_STORAGE_DIR', os.path.join(os.getcwd(), 'bills'))
    BILL_RETENTION_MONTHS = int(os.getenv('BILL_RETENTION_MONTHS', 3))  # 账单保留月数，默认3个月

    # 微信支付回调IP白名单
    WECHAT_PAY_IP_WHITELIST = os.getenv('WECHAT_PAY_IP_WHITELIST', '').split(',') if os.getenv(
        'WECHAT_PAY_IP_WHITELIST') else [
        # 微信支付回调IP白名单，参考：https://pay.weixin.qq.com/doc/v3/merchant/4012075420
        '127.0.0.1',  # 本地开发环境
        # 支付回调通知IP段
        '*************/25',  # 上海电信出口网段
        '************/25',  # 上海联通出口网段
        '*************/25',  # 上海CAP出口网段
        '***********/25',  # 深圳电信出口网段
        '***********/25',  # 深圳联通出口网段
        '*************/25',  # 深圳CAP出口网段
        '***************/25',  # 香港出口网段
        # 退款结果通知、分账动账通知IP
        '**************',
        '*************',
        '**************',
        '**************',
        '***************',
        '************'
    ]

    ADMIN_WECHAT_APPID = os.getenv('ADMIN_WECHAT_APPID')
    ADMIN_WECHAT_SECRET = os.getenv('ADMIN_WECHAT_SECRET')  # 保密！

    KUAIDI100_SERVICE_KEY = os.getenv('KUAIDI100_SERVICE_KEY')  # 快递100的key
    KUAIDI100_SERVICE_SECRET = os.getenv('KUAIDI100_SERVICE_SECRET')  # 保密！

    TOKEN_EXPIRE_HOURS = int(os.getenv('TOKEN_EXPIRE_HOURS', 4))
    CAROUSEL_CACHE_TTL = os.getenv('CAROUSEL_CACHE_TTL', 300)
    ORDER_EXPIRE_MINUTES = int(os.getenv('ORDER_EXPIRE_MINUTES', 30))  # 订单过期时间（分钟）
    ORDER_AUTO_COMPLETE_MINUTES = int(os.getenv('ORDER_AUTO_COMPLETE_MINUTES', 60 * 24))  # 订单自动完成时间（分钟）

    # 测试模式支付配置（开发环境使用）
    ENABLE_TEST_PAYMENT = os.getenv('ENABLE_TEST_PAYMENT', 'False').lower() in ('true', '1', 't')

    DEFAULT_AVATAR_URL = os.getenv(
        'DEFAULT_AVATAR_URL',
        'https://static.znyx.xiaochaitec.cn/images/default_avatar.jpg'
    )  # 默认用户头像
    DEFAULT_ADMIN_AVATAR_URL = os.getenv(
        'DEFAULT_ADMIN_AVATAR_URL',
        'https://static.znyx.xiaochaitec.cn/images/default_admin_avatar.jpg'
    )  # 默认管理员头像
    DEFAULT_SHOP_AVATAR_URL = os.getenv(
        'DEFAULT_SHOP_AVATAR_URL',
        'https://static.znyx.xiaochaitec.cn/images/wechat/default/default-store.png'
    )  # 默认店铺头像
    DEFAULT_QRCODE_URL = os.getenv(
        'DEFAULT_QRCODE_URL',
        'https://static.znyx.xiaochaitec.cn/images/wechat/default/default-qrcode.png'
    )  # 默认二维码

    @classmethod
    def validate(cls):
        """基础配置校验（开发/生产环境通用）"""
        errors = []

        # 关键配置存在性检查
        required_keys = [
            'SQLALCHEMY_DATABASE_URI',
            'REDIS_URL',
            'ENCRYPT_KEY',
            'WECHAT_APPID',
            'WECHAT_SECRET'
        ]
        for key in required_keys:
            if not getattr(cls, key, None):
                errors.append(f"缺少必要配置项: {key}")

        # Redis连接格式校验
        if cls.REDIS_URL:
            try:
                redis_parsed = urlparse(cls.REDIS_URL)
                if redis_parsed.scheme != 'redis':
                    errors.append("REDIS_URL 必须以 redis:// 开头")
            except ValueError:
                errors.append("REDIS_URL 格式错误")

        # 加密密钥长度校验（Fernet需要32位base64）
        if cls.ENCRYPT_KEY:
            try:
                Fernet(cls.ENCRYPT_KEY)  # 利用加密库自带的校验
            except ValueError:
                errors.append("ENCRYPT_KEY 格式无效，需32位URL安全的base64编码")

        # 创建必要的目录
        try:
            import os
            # 确保图片存储目录存在
            os.makedirs(cls.IMAGE_STORAGE_DIR, exist_ok=True)
            # 确保账单存储目录存在
            os.makedirs(cls.BILL_STORAGE_DIR, exist_ok=True)
        except Exception as e:
            errors.append(f"创建存储目录失败: {str(e)}")

        if errors:
            raise APIError(
                ErrorCode.DATABASE_CONNECTION_FAILED,
                "配置校验失败",
                extra={"errors": errors}
            )


class DevelopmentConfig(Config):
    DEBUG = True

    # 数据库调试模式
    SQLALCHEMY_ECHO = True  # 输出SQL语句
    SQLALCHEMY_RECORD_QUERIES = True  # 记录查询性能

    # 迁移策略优化配置
    MIGRATE_COMPARE_TYPE = True  # 深度字段类型对比
    MIGRATE_COMPARE_SERVER_DEFAULT = True  # 对比字段默认值
    MIGRATE_IGNORE_UNKNOWN_CHANGES = False  # 严格检查未知变更

    EXPOSE_ERROR_DETAILS = True
    ENABLE_TEST_PAYMENT = True

    PRODUCTS_CACHE_TTL = 60  # 开发环境缓存60秒

    @classmethod
    def validate(cls):
        """开发环境宽松校验"""
        try:
            super().validate()
        except APIError as e:
            if "ENCRYPT_KEY" in str(e):  # 允许开发环境使用测试密钥
                current_app.logger.warning("开发环境使用测试加密密钥")
            else:
                raise


class ProductionConfig(Config):
    DEBUG = False
    EXPOSE_ERROR_DETAILS = False

    # 日志配置
    LOG_TO_FILE = True
    LOG_LEVEL = 'INFO'
    LOG_FILE = '/srv/zhenxuantuan/logs/flask_app.log'
    LOG_MAX_SIZE = 100 * 1024 * 1024  # 100MB
    LOG_BACKUP_COUNT = 10

    PRODUCTS_CACHE_TTL = 300  # 生产环境缓存5分钟

    @classmethod
    def validate(cls):
        """生产环境额外校验"""
        super().validate()  # 先执行基础校验
        errors = []

        # 生产环境强制规则
        if cls.DEBUG:
            errors.append("生产环境禁止开启DEBUG模式")

        if not cls.LOG_FILE.startswith('/'):
            errors.append("生产环境日志路径必须为绝对路径")

        # 数据库连接池合理性检查
        pool_size = cls.SQLALCHEMY_ENGINE_OPTIONS['pool_size']
        max_overflow = cls.SQLALCHEMY_ENGINE_OPTIONS['max_overflow']
        if pool_size > 20 or max_overflow > 30:
            errors.append("数据库连接池配置超出安全范围")

        if errors:
            raise APIError(
                ErrorCode.DATABASE_CONNECTION_FAILED,
                "生产环境配置校验失败",
                extra={"errors": errors}
            )


class StagingConfig(ProductionConfig):
    """模拟生产环境的配置（如预发布环境）"""
    EXPOSE_ERROR_DETAILS = True

    CAROUSEL_CACHE_TTL = os.getenv('CAROUSEL_CACHE_TTL', 30)


# 对于持续集成环境（CI）
class TestingConfig(Config):
    TESTING = True
    MIGRATE_COMPARE_TYPE = True
    MIGRATE_COMPARE_SERVER_DEFAULT = True
    MIGRATE_IGNORE_UNKNOWN_CHANGES = False


config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'staging': StagingConfig,
    'default': DevelopmentConfig
}


def configure_app(app, config_name):
    """加载配置"""
    app.config.from_object(config[config_name])
    try:
        config[config_name].validate()
    except APIError as e:
        app.logger.critical(f"配置校验失败: {str(e)}")
        raise
