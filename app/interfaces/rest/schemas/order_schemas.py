from marshmallow import Schema, fields, validate

from app.domain.order.value_objects import RefundReason, SubOrderStatus
from app.schemas.home import PaginationSchema


class ProductOrderItemSchema(Schema):
    """商品直接下单项Schema"""
    product_id = fields.Integer(required=True, data_key='productId')
    sku_id = fields.Integer(required=True, data_key='sku')
    quantity = fields.Integer(required=True, validate=validate.Range(min=1), data_key='num')


class CreateOrderSchema(Schema):
    """下单Schema"""
    address_id = fields.Integer(required=True, data_key='addressId')
    remark = fields.String(allow_none=True, missing='')
    coupon_ids = fields.List(fields.Integer(), required=False, missing=None, allow_none=True, data_key='couponIds')
    estimated_amount = fields.Decimal(required=True, places=2, data_key='estimatedAmount')


class CreateOrderFromProductSchema(CreateOrderSchema):
    """商品页直接下单Schema"""
    product = fields.Nested(ProductOrderItemSchema, required=True)


class CreateOrderFromCartSchema(CreateOrderSchema):
    """购物车下单Schema"""
    cart_item_ids = fields.List(fields.Integer(), required=True, data_key='cartItemIds')


# 新增订单预览Schema
class OrderPreviewFromProductSchema(Schema):
    """商品页订单预览Schema"""
    product = fields.Nested(ProductOrderItemSchema, required=True)
    coupon_ids = fields.List(fields.Integer(), required=False, missing=None, allow_none=True, data_key='couponIds')


class OrderPreviewFromCartSchema(Schema):
    """购物车订单预览Schema"""
    cart_item_ids = fields.List(fields.Integer(), required=True, data_key='cartItemIds')
    coupon_ids = fields.List(fields.Integer(), required=False, missing=None, allow_none=True, data_key='couponIds')


order_no_field = fields.String(
    required=True,
    data_key='orderNo',
    validate=validate.Regexp(
        regex=r'^\d{18}(-\d+)?$',
        error="订单号格式无效，应为18位数字或带子订单号格式（如'123456789012345678-1'）"
    )
)


class CancelOrderSchema(Schema):
    """取消订单Schema"""
    order_no = order_no_field


class RepayOrderSchema(Schema):
    """重新支付订单Schema"""
    order_no = order_no_field


class GetOrderByNoSchema(Schema):
    """按订单号查询订单Schema"""
    order_no = order_no_field


class RefundOrderSchema(Schema):
    """订单退款Schema"""
    order_no = order_no_field
    reason = fields.String(
        required=True,
        data_key='reason',
        validate=validate.OneOf([reason.value for reason in RefundReason]),
        description="退款原因"
    )


class DeleteOrderSchema(Schema):
    """删除订单Schema"""
    order_no = order_no_field


class PendingOrdersSchema(PaginationSchema):
    """待支付订单列表查询Schema"""
    pass


class ListAllOrdersSchema(PaginationSchema):
    """所有订单列表查询Schema"""
    pass


class PaidOrdersSchema(PaginationSchema):
    """已支付订单列表查询Schema"""
    pass


class CompletedOrdersSchema(PaginationSchema):
    """已完成订单列表查询Schema"""
    pass


class ShippingOrdersSchema(PaginationSchema):
    """运输中订单列表查询Schema"""
    pass


class AdminShipOrderSchema(Schema):
    """管理员发货Schema"""
    order_no = order_no_field
    logistics_company = fields.String(required=True, data_key='logisticsCompany')
    tracking_number = fields.String(required=True, data_key='trackingNumber')


class AdminGetPaidOrdersSchema(PaginationSchema):
    """管理员获取已付款订单列表查询Schema"""
    pass


class AdminGetAfterSalePendingOrdersSchema(PaginationSchema):
    """管理员获取待售后子订单列表查询Schema"""
    pass


class AdminQuerySubOrdersSchema(PaginationSchema):
    """管理员查询子订单Schema"""
    order_query = fields.String(
        required=True,
        data_key='orderQuery',
        description="主订单号后四位或完整子订单号")
    status = fields.String(
        required=True,
        data_key='status',
        validate=validate.OneOf([status.value for status in SubOrderStatus] + ["ship", "all", "today"]),
        description="子订单状态，使用'ship'可查询所有物流相关状态（已发货、已签收、已拒签），'all'查询所有子订单，'today'查询今日创建的子订单")


class AdminSubOrderDetailSchema(Schema):
    """管理员查询子订单详情Schema"""
    order_no = order_no_field


class AdminAfterSalePendingDetailSchema(Schema):
    """管理员查询待售后子订单详情Schema"""
    order_no = order_no_field


class AdminGetShippedOrdersSchema(PaginationSchema):
    """管理员获取已发货订单列表查询Schema"""
    pass


class AdminGetCompletedOrdersSchema(PaginationSchema):
    """管理员获取已完成订单列表查询Schema"""
    pass


class AdminGetAllOrdersSchema(PaginationSchema):
    """管理员获取所有订单列表查询Schema"""
    pass


class AdminGetTodayOrdersSchema(PaginationSchema):
    """管理员获取今日订单列表查询Schema"""
    pass


class LogisticsDetailSchema(Schema):
    """物流详情查询参数验证"""
    order_no = order_no_field


class CompleteOrderSchema(Schema):
    """完成订单Schema"""
    order_no = order_no_field


class ApplyAfterSaleSchema(Schema):
    """申请售后Schema"""
    order_no = order_no_field
    apply_type = fields.String(
        required=True,
        data_key='applyType',
        validate=validate.OneOf(["RefundOnly-Receive", "RefundOnly-Rejected"]),
        description="申请类型：已收到商品仅退款/未收到商品仅退款"
    )
    description = fields.String(required=True, description="申请说明")
    evidence_images = fields.List(
        fields.String(),
        required=False,
        missing=None,
        data_key='images',
        description="证明图片URL列表"
    )


class RejectAfterSaleSchema(Schema):
    """拒绝售后申请Schema"""
    order_no = order_no_field
    description = fields.String(required=True, description="拒绝原因说明")


class RefundAfterSaleSchema(Schema):
    """售后退款处理Schema"""
    order_no = order_no_field
    description = fields.String(required=True, description="处理说明")
    refund_amount = fields.Decimal(
        required=True,
        places=2,
        data_key='refundAmount',
        description="退款金额"
    )


class GetAfterSaleListSchema(PaginationSchema):
    """获取售后列表Schema"""
    pass


class GetAfterSaleDetailSchema(Schema):
    """获取售后详情Schema"""
    order_no = order_no_field


# 添加获取子订单列表Schema
class GetSubOrdersByParentNoSchema(Schema):
    """根据母订单号获取子订单列表Schema"""
    order_no = fields.String(
        required=True,
        data_key='orderNo',
        validate=validate.Regexp(
            regex=r'^\d{18}$',
            error="订单号格式无效，应为18位数字"
        )
    )
