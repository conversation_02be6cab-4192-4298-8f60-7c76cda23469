from flask import Blueprint, current_app, send_file

from app.constants import ErrorCode
from app.decorators.auth import admin_required
from app.decorators.validation import validate_with
from app.infrastructure.payment.factory import PaymentServiceFactory
from app.interfaces.rest.schemas.bill_schemas import BillDownloadSchema
from app.utils.errors import APIError

# 创建蓝图
bp = Blueprint('admin_bills', __name__, url_prefix='/api/admin/bills')


@bp.post('/download')
@admin_required
@validate_with(BillDownloadSchema)
def download_bill(bill_type, bill_date):
    """管理员下载微信支付账单

    接收账单类型和日期，检查本地是否有该账单文件，
    如果没有则从微信支付平台下载，然后返回文件名

    Args:
        bill_type: 账单类型，tradebill（交易账单）或 fundflowbill（资金账单）
        bill_date: 账单日期，格式：YYYY-MM-DD

    Returns:
        文件名或错误信息
    """
    try:
        # 获取支付服务实例
        payment_service = PaymentServiceFactory.create_payment_service()

        # 验证账单日期是否有效
        date_validation = payment_service.is_bill_date_valid(bill_date)
        if not date_validation['valid']:
            raise APIError(ErrorCode.BUSINESS_LOGIC_ERROR, date_validation['error'])

        # 检查本地是否已有该账单文件
        existing_file_path = payment_service.get_bill_file_path(bill_type, bill_date)

        if existing_file_path:
            # 本地文件存在，直接返回文件名
            current_app.logger.info(f"返回本地账单文件名: {bill_type}_{bill_date}")
            return {
                'code': ErrorCode.SUCCESS.code,
                'data': {
                    'filename': f"{bill_type}_{bill_date}.csv"
                }
            }

        # 本地文件不存在，需要从微信支付平台下载
        current_app.logger.info(f"开始下载账单: {bill_type}_{bill_date}")

        # 根据账单类型申请账单
        if bill_type == 'tradebill':
            apply_result = payment_service.apply_trade_bill(bill_date)
        elif bill_type == 'fundflowbill':
            apply_result = payment_service.apply_fundflow_bill(bill_date)
        else:
            raise APIError(ErrorCode.BUSINESS_LOGIC_ERROR, "不支持的账单类型")

        if not apply_result['success']:
            raise APIError(
                ErrorCode.BUSINESS_LOGIC_ERROR,
                f"申请账单失败: {apply_result['error_message']}"
            )

        # 获取下载链接
        download_url = apply_result['download_url']
        if not download_url:
            raise APIError(ErrorCode.BUSINESS_LOGIC_ERROR, "未获取到账单下载链接")

        # 下载账单文件
        download_result = payment_service.download_bill(download_url, bill_type, bill_date)

        if not download_result['success']:
            raise APIError(
                ErrorCode.BUSINESS_LOGIC_ERROR,
                f"下载账单失败: {download_result['error_message']}"
            )

        # 返回文件名
        current_app.logger.info(f"账单下载成功: {bill_type}_{bill_date}")
        return {
            'code': ErrorCode.SUCCESS.code,
            'data': {
                'filename': f"{bill_type}_{bill_date}.csv"
            }
        }

    except APIError:
        # 重新抛出API错误
        raise
    except Exception as e:
        current_app.logger.error(f"下载账单失败: {str(e)}", exc_info=True)
        raise APIError(ErrorCode.INTERNAL_SERVER_ERROR, "下载账单失败")


@bp.get('/file/<filename>')
@admin_required
def get_bill_file(filename):
    """获取账单文件

    根据文件名返回对应的账单文件

    Args: 
        filename: 账单文件名

    Returns:
        文件下载响应或错误信息
    """
    try:
        # 获取支付服务实例
        payment_service = PaymentServiceFactory.create_payment_service()

        # 从文件名中提取账单类型和日期
        try:
            bill_type, bill_date = filename.replace('.csv', '').split('_')
        except ValueError:
            raise APIError(ErrorCode.VALIDATION_ERROR, "无效的文件名格式")

        # 获取文件路径
        file_path = payment_service.get_bill_file_path(bill_type, bill_date)

        if not file_path:
            raise APIError(ErrorCode.NO_STATEMENT_EXIST, "账单文件不存在")

        # 返回文件
        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='text/csv'
        )

    except APIError:
        # 重新抛出API错误
        raise
    except Exception as e:
        current_app.logger.error(f"获取账单文件失败: {str(e)}", exc_info=True)
        raise APIError(ErrorCode.INTERNAL_SERVER_ERROR, "获取账单文件失败")


@bp.get('/status/<bill_type>/<bill_date>')
@admin_required
def check_bill_status(bill_type, bill_date):
    """检查账单状态

    检查指定日期和类型的账单是否存在于本地

    Args:
        bill_type: 账单类型，tradebill 或 fundflowbill
        bill_date: 账单日期，格式：YYYY-MM-DD

    Returns:
        账单状态信息
    """
    try:
        # 验证账单类型
        if bill_type not in ['tradebill', 'fundflowbill']:
            raise APIError(ErrorCode.BUSINESS_LOGIC_ERROR, "不支持的账单类型")

        # 获取支付服务实例
        payment_service = PaymentServiceFactory.create_payment_service()

        # 验证账单日期是否有效
        date_validation = payment_service.is_bill_date_valid(bill_date)
        if not date_validation['valid']:
            raise APIError(ErrorCode.BUSINESS_LOGIC_ERROR, date_validation['error'])

        # 检查本地是否已有该账单文件
        existing_file_path = payment_service.get_bill_file_path(bill_type, bill_date)

        return {
            'code': ErrorCode.SUCCESS.code,
            'data': {
                'bill_type': bill_type,
                'bill_date': bill_date,
                'exists': existing_file_path is not None,
                'file_path': existing_file_path
            }
        }

    except APIError:
        # 重新抛出API错误
        raise
    except Exception as e:
        current_app.logger.error(f"检查账单状态失败: {str(e)}", exc_info=True)
        raise APIError(ErrorCode.INTERNAL_SERVER_ERROR, "检查账单状态失败")
