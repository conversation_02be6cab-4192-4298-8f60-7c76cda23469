from flask import Blueprint, g, current_app

from app.application.coupon.use_cases.coupon_allocation import GetAllCouponAllocationsUseCase, \
    CreateCouponAllocationUseCase, BulkCreateGroupLeaderAllocationUseCase, GetGroupLeaderChannelAllocationsUseCase
from app.application.coupon.use_cases.group_leader_sub_allocation import GetAllGroupLeaderSubAllocationsForAdminUseCase
from app.application.coupon.use_cases.public_coupon import GetPublicCouponsForAdminUseCase
from app.constants import ErrorCode
from app.decorators.auth import admin_required
from app.decorators.validation import validate_with
from app.domain.coupon.exceptions import CouponException
from app.domain.coupon.services import CouponManagementService, GroupLeaderAllocationService
from app.extensions import rdb
from app.infrastructure.coupon.repositories import SQLCouponAllocationRepository, SQLGroupLeaderAllocationRepository, \
    SQLGroupLeaderSubAllocationRepository, SQLPublicGroupLeaderAllocationRepository, SQLUserCouponRepository
from app.infrastructure.user.repositories import SQLGroupLeaderRepository, SQLUserRepository
from app.interfaces.rest.schemas.coupon import CouponAllocationCreateWrapperSchema, \
    BulkGroupLeaderAllocationCreateSchema, CouponAllocationQuerySchema
from app.utils.errors import APIError

bp = Blueprint('admin_coupon', __name__, url_prefix='/api/admin/coupon/allocations')


@bp.post('')
@admin_required
@validate_with(CouponAllocationCreateWrapperSchema)
def create_allocation(allocation):
    """管理员创建优惠券（一次）分配"""
    try:
        use_case = CreateCouponAllocationUseCase(
            service=CouponManagementService(),
            repo=SQLCouponAllocationRepository()
        )
        allocation = use_case.execute(allocation, g.auth['admin_user_id'])

        return {
            'code': ErrorCode.SUCCESS.code,
            'data': {'id': allocation.id}
        }
    except CouponException as e:
        raise APIError(ErrorCode.DATA_ISSUE, str(e))
    except Exception as e:
        raise APIError(ErrorCode.BUSINESS_LOGIC_ERROR, f"创建失败: {str(e)}")


@bp.get('')
@admin_required
@validate_with(CouponAllocationQuerySchema)
def get_allocations(page, per_page, status):
    """管理员分页查询优惠券（一次）分配"""
    try:
        use_case = GetAllCouponAllocationsUseCase(
            repo=SQLCouponAllocationRepository()
        )
        result = use_case.execute(page, per_page, status)
        return {
            'code': ErrorCode.SUCCESS.code,
            'data': result
        }
    except Exception as e:
        raise APIError(ErrorCode.BUSINESS_LOGIC_ERROR, f"查询失败: {str(e)}")


@bp.get('/leader-channel')
@admin_required
def get_leader_channel_allocations():
    """管理员查询设置为团长渠道的、没过最晚可发放时间的一次分配优惠券（不使用分页）"""
    try:
        use_case = GetGroupLeaderChannelAllocationsUseCase(
            allocation_repo=SQLCouponAllocationRepository(),
            public_allocation_repo=SQLPublicGroupLeaderAllocationRepository()
        )
        result = use_case.execute()
        return {
            'code': ErrorCode.SUCCESS.code,
            'data': result
        }
    except Exception as e:
        raise APIError(ErrorCode.BUSINESS_LOGIC_ERROR, f"查询团长渠道优惠券失败: {str(e)}")


@bp.post('/bulk-group-leader-allocations')
@admin_required
@validate_with(BulkGroupLeaderAllocationCreateSchema)
def create_bulk_group_leader_allocation(data):
    """批量创建团长二次分配"""
    use_case = BulkCreateGroupLeaderAllocationUseCase(
        sec_alloc_service=GroupLeaderAllocationService(
            sub_allocation_repo=SQLGroupLeaderSubAllocationRepository(),
            logger=current_app.logger,
            redis_client=rdb.client
        ),
        sec_alloc_repo=SQLGroupLeaderAllocationRepository(),
        pri_alloc_repo=SQLCouponAllocationRepository(),
        leader_repo=SQLGroupLeaderRepository(),
        public_alloc_repo=SQLPublicGroupLeaderAllocationRepository()
    )

    results = use_case.execute(
        coupon_allocation_id=data['coupon_allocation_id'],
        total_quota=data['total_quota'],
        leader_ids=data['leader_ids'],
        operator_id=g.auth['admin_user_id']
    )

    return {
        'code': ErrorCode.SUCCESS.code,
        'data': results
    }


@bp.get('/sub-allocations')
@admin_required
def get_all_sub_allocations():
    """管理员获取所有优惠券三次分配信息"""
    use_case = GetAllGroupLeaderSubAllocationsForAdminUseCase(
        sub_alloc_repo=SQLGroupLeaderSubAllocationRepository(),
        leader_alloc_repo=SQLGroupLeaderAllocationRepository(),
        coupon_alloc_repo=SQLCouponAllocationRepository(),
        user_coupon_repo=SQLUserCouponRepository(),
        user_repo=SQLUserRepository()
    )
    result = use_case.execute()
    return {
        'code': ErrorCode.SUCCESS.code,
        'data': result
    }


@bp.get('/public-coupons')
@admin_required
def get_public_coupons():
    """管理员获取公开优惠券信息"""
    use_case = GetPublicCouponsForAdminUseCase(
        coupon_alloc_repo=SQLCouponAllocationRepository(),
        user_coupon_repo=SQLUserCouponRepository(),
        user_repo=SQLUserRepository()
    )
    result = use_case.execute()
    return {
        'code': ErrorCode.SUCCESS.code,
        'data': result
    }
