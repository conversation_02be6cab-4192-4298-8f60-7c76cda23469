from enum import Enum


class ErrorCode(Enum):
    """错误码枚举（代码 + 消息一体化）"""

    # 基础状态
    SUCCESS = (0, "成功")
    SUCCEED = (1, "已成功，请刷新")
    SYSTEM_BUSY = (-1, "服务器繁忙，请稍后再试")
    UNKNOWN_ERROR = (9999, "未知错误")

    # 用户输入错误（1xxx）
    VALIDATION_ERROR = (1000, "请求参数不合法")
    MISSING_PARAMETER = (1001, "缺少必要参数")

    # 认证错误（2xxx）
    AUTH_ERROR = (2000, "认证失败")
    USER_NOT_REGISTERED = (2001, "用户未注册")

    # 业务逻辑错误（3xxx）
    BUSINESS_LOGIC_ERROR = (3000, "业务逻辑错误")
    RATE_LIMIT_EXCEEDED = (3001, "请求频率过高，请稍后再试")
    OPTIMISTIC_LOCK_CONFLICT = (3002, "数据版本冲突，请刷新后重试")
    USER_NOT_IN_TARGET_GROUP = (3003, "用户不属于该团长团队")
    INVALID_ALLOCATION_LINK = (3004, "分配链接已失效")
    DAILY_LIMIT_EXCEEDED = (3005, "今日领取次数已达上限")
    USER_LIMIT_EXCEEDED = (3006, "用户累计领取次数已达上限")
    HOLDING_LIMIT_EXCEEDED = (3007, "用户当前持有数量已达上限")
    GROUP_LEADER_ALREADY_EXISTS = (3008, "该团长已存在")
    INVALID_INVITE_CODE = (3009, "无效的邀请码")
    PRICE_CHANGED = (3010, "价格已发生变化，请重新确认")
    ORDER_CREATE_FAILED = (3011, "创建订单失败")
    COUPON_NOT_APPLICABLE = (3012, "优惠券不适用于当前商品")
    COUPON_THRESHOLD_NOT_MET = (3013, "订单金额未满足优惠券使用条件")
    COUPON_USED = (3014, "优惠券已使用")
    INVALID_ORDER_STATUS = (3015, "订单状态无效")
    COMMISSION_BALANCE_INSUFFICIENT = (3016, "佣金余额不足")
    COMMISSION_AMOUNT_INVALID = (3017, "佣金金额无效")
    WITHDRAWAL_AMOUNT_INVALID = (3018, "提现金额无效")

    # 数据问题（4xxx）
    DATA_ISSUE = (4000, "数据问题")
    USER_CREATION_CONFLICT = (4001, "用户创建冲突")
    USER_NOT_FOUND = (4002, "用户不存在")
    STOCK_INSUFFICIENT = (4003, "库存不足")
    DATA_CONFLICT = (4004, "数据冲突")
    PREPAID_INSUFFICIENT = (4005, "预扣额度不足")
    PRODUCT_NOT_FOUND = (4006, "商品不存在或已下架")
    SKU_NOT_FOUND = (4007, "商品规格不存在或已下架")
    ADDRESS_NOT_FOUND = (4008, "收货地址不存在")
    COUPON_NOT_FOUND = (4009, "优惠券不存在或已过期")
    CART_ITEM_NOT_FOUND = (4010, "购物车商品不存在")
    CART_ITEM_CHANGED = (4011, "购物车项已改变")
    PRODUCT_SKU_UNAVAILABLE = (4012, "商品规格不可用")
    CART_ITEM_INVALID = (4013, "购物车项无效")
    ORDER_NOT_FOUND = (4014, "订单不存在")
    SHIPPING_LOCATION_NOT_FOUND = (4015, "发货地不存在")
    ORDER_NOT_SHIPPED = (4016, "订单未发货")
    NO_STATEMENT_EXIST = (4017, "账单不存在")

    # 第三方服务错误（5xxx）
    THIRD_PARTY_ERROR = (5000, "第三方服务错误")
    WECHAT_API_ERROR = (5001, "微信接口未知错误")
    WECHAT_INVALID_CODE = (5002, "微信登录凭证无效")
    WECHAT_API_LIMIT = (5003, "微信接口调用频率超限")
    USER_BLOCKED = (5004, "账号存在安全风险")
    WECHAT_SYSTEM_ERROR = (5005, "微信服务异常")
    THIRD_PARTY_TIMEOUT = (5006, "第三方超时")
    WECHAT_PAY_ERROR = (5007, "微信支付异常")

    # 服务器内部错误（6xxx）
    INTERNAL_SERVER_ERROR = (6000, "服务器内部错误")
    REDIS_NOT_INITIALIZED = (6001, "Redis客户端未初始化")
    REDIS_CONNECTION_FAILED = (6002, "Redis连接失败")
    DATABASE_CONNECTION_FAILED = (6003, "数据库连接异常")

    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message

    @classmethod
    def get_by_code(cls, code: int) -> "ErrorCode":
        """根据错误码数值获取枚举实例"""
        return next((e for e in cls if e.code == code), cls.UNKNOWN_ERROR)
