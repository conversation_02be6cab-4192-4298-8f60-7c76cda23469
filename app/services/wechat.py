from datetime import datetime
from zoneinfo import ZoneInfo

import requests
from flask import current_app, request
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from app.constants.error_codes import ErrorCode
from app.extensions.redis import rdb
from app.utils.errors import APIError, RetryableError, InternalServerError


class WeChatAPIClient:
    """微信API客户端（包含智能重试和连接优化）"""

    def __init__(self, appid: str, secret: str):
        self.appid = appid
        self.secret = secret

        # 配置优化后的Session
        self.session = requests.Session()
        self._configure_adapter()

        # 快递100编码到微信编码的映射字典
        self.express_code_mapping = {
            "zhongtong": {"delivery_id": "ZTO", "delivery_name": "中通快递"},
            "yunda": {"delivery_id": "YD", "delivery_name": "韵达速递"},
            "yuantong": {"delivery_id": "YTO", "delivery_name": "圆通速递"},
            "shentong": {"delivery_id": "STO", "delivery_name": "申通快递"},
            "huitongkuaidi": {"delivery_id": "HTKY", "delivery_name": "百世快递"},
            "tiantian": {"delivery_id": "TTKDEX", "delivery_name": "天天快递"},
            "youzhengguonei": {"delivery_id": "CHINAPOST", "delivery_name": "中国邮政"},
            "youshuwuliu": {"delivery_id": "UC", "delivery_name": "优速快递"},
            "ems": {"delivery_id": "EMS", "delivery_name": "EMS"},
            "shunfeng": {"delivery_id": "SF", "delivery_name": "顺丰速运"},
            "jtexpress": {"delivery_id": "JTSD", "delivery_name": "极兔速递"},
            "zhaijisong": {"delivery_id": "ZJS", "delivery_name": "宅急送"},
            "debangkuaidi": {"delivery_id": "DBL", "delivery_name": "德邦快递"},
            "jd": {"delivery_id": "JD", "delivery_name": "京东快递"},
            "aramex": {"delivery_id": "ARAMEX", "delivery_name": "Aramex"},
            "annengwuliu": {"delivery_id": "ANEKY", "delivery_name": "安能物流"},
            "auspost": {"delivery_id": "AUSTRALIA", "delivery_name": "Australia Post Tracking"},
            "baishiwuliu": {"delivery_id": "BTWL", "delivery_name": "百世快运"},
            "bestexp": {"delivery_id": "BAISHIGUOJI", "delivery_name": "百世跨境"},
            "bluedart": {"delivery_id": "BLUEDART", "delivery_name": "Bluedart"},
            "cosco": {"delivery_id": "COSCO", "delivery_name": "中远E环球"},
            "dhl": {"delivery_id": "DHL", "delivery_name": "DHL"},
            "dpd": {"delivery_id": "DPD", "delivery_name": "DPD"},
            "fedex": {"delivery_id": "FEDEX", "delivery_name": "FEDEX联邦(国内件）"},
            "gls": {"delivery_id": "GLS", "delivery_name": "GLS"},
            "jtexpressmy": {"delivery_id": "JTEXPRESSMY", "delivery_name": "J&TExpress(MY)"},
            "postnl": {"delivery_id": "POSTNL", "delivery_name": "荷兰邮政"},
            "sfwl": {"delivery_id": "SFWL", "delivery_name": "盛丰物流"},
            "suning": {"delivery_id": "SNWL", "delivery_name": "苏宁物流"},
            "tnt": {"delivery_id": "TNT", "delivery_name": "TNT快递"},
            "ups": {"delivery_id": "UPS", "delivery_name": "UPS"},
            "usps": {"delivery_id": "USPS", "delivery_name": "USPS美国邮政"},
            "yuntrack": {"delivery_id": "YUNTRACK", "delivery_name": "YunExpress"}
        }

    def _configure_adapter(self):
        """配置连接适配器"""
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,  # 连接池数量
            pool_maxsize=100,  # 最大连接数
            max_retries=2  # 底层自动重试
        )
        self.session.mount('https://', adapter)

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type(RetryableError),
        reraise=True)
    def code_to_session(self, code: str) -> dict:
        """微信登录凭证校验（统一错误处理及智能重试）"""
        url = "https://api.weixin.qq.com/sns/jscode2session"

        try:
            # 记录请求开始
            current_app.logger.debug(
                "WECHAT_API_CALL | code:%s*** | endpoint:jscode2session",
                code[:8]
            )

            response = self.session.get(
                url,
                params={
                    "appid": self.appid,
                    "secret": self.secret,
                    "js_code": code,
                    "grant_type": "authorization_code"
                },
                timeout=5  # 包含连接和读取超时
            )
            response.raise_for_status()
            data = response.json()

            # 统一错误处理
            if 'errcode' in data and data['errcode'] != 0:
                errcode = data['errcode']
                errmsg = data.get('errmsg', '未知错误')

                # 记录完整错误信息
                current_app.logger.error(
                    f"WECHAT_API_ERROR | code: {code[:8]}***, errcode: {errcode}, errmsg: {errmsg}"
                )

                # 分类处理已知错误
                if errcode == 40029:  # js_code 无效
                    raise APIError(error_code=ErrorCode.WECHAT_INVALID_CODE)
                elif errcode == 45011:  # API 调用太频繁，请稍候再试
                    raise RetryableError(error_code=ErrorCode.WECHAT_API_LIMIT)  # 自动触发重试
                elif errcode == 40226:  # 高风险等级用户，小程序登录拦截
                    current_app.logger.warning(
                        "SECURITY_INTERCEPTION 微信高风险用户拦截 | openid: %s, client_ip: %s, risk_info: %s",
                        data.get('openid'), request.remote_addr, data.get('risk_info')
                    )
                    raise APIError(error_code=ErrorCode.USER_BLOCKED)
                elif errcode == -1:  # 系统繁忙，稍候再试
                    raise RetryableError(error_code=ErrorCode.WECHAT_SYSTEM_ERROR)  # 自动触发重试
                else:  # 其他未明确处理的错误码
                    raise APIError(error_code=ErrorCode.WECHAT_API_ERROR)

            return data

        except APIError as e:
            raise e

        except requests.exceptions.Timeout as e:
            current_app.logger.warning(f"WECHAT_TIMEOUT 微信接口超时: {str(e)}")
            raise RetryableError(error_code=ErrorCode.THIRD_PARTY_TIMEOUT) from e

        except requests.exceptions.RequestException as e:
            current_app.logger.warning(f"WECHAT_HTTP_ERROR 微信接口异常: {str(e)}")
            raise APIError(error_code=ErrorCode.THIRD_PARTY_ERROR) from e

        except Exception as e:
            current_app.logger.exception("UNKNOWN_ERROR 未处理的微信接口异常")
            raise APIError(error_code=ErrorCode.UNKNOWN_ERROR) from e

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type(RetryableError),
        reraise=True)
    def get_stable_access_token(self, force_refresh=False) -> str:
        """
        获取稳定版接口调用凭据

        根据官方最佳实践：
        1. 平台会提前5分钟更新access_token
        2. 无须考虑并行调用导致的意外情况

        Args:
            force_refresh: 是否强制刷新（默认False）

        Returns:
            str: 获取到的access_token
        """
        try:
            # 尝试从缓存获取
            redis_key = f"wechat:access_token:{self.appid}"
            if not force_refresh:
                cached_token = rdb.client.get(redis_key)
                if cached_token:
                    current_app.logger.debug("使用缓存的微信access_token")
                    return cached_token

            # 从微信API获取新token
            current_app.logger.info(
                f"获取新的微信access_token | force_refresh:{force_refresh}"
            )

            url = "https://api.weixin.qq.com/cgi-bin/stable_token"
            data = {
                "grant_type": "client_credential",
                "appid": self.appid,
                "secret": self.secret,
                "force_refresh": force_refresh
            }

            response = self.session.post(url, json=data, timeout=5)
            response.raise_for_status()
            result = response.json()

            # 处理错误
            if 'errcode' in result and result['errcode'] != 0:
                errcode = result['errcode']
                errmsg = result.get('errmsg', '未知错误')

                current_app.logger.error(
                    f"WECHAT_TOKEN_ERROR | errcode:{errcode}, errmsg:{errmsg}"
                )

                if errcode == 45009:  # 调用超过天级别频率限制
                    raise APIError(error_code=ErrorCode.WECHAT_API_LIMIT)
                elif errcode == 45011:  # API调用太频繁
                    raise RetryableError(error_code=ErrorCode.WECHAT_API_LIMIT)
                elif errcode == -1:  # 系统繁忙
                    raise RetryableError(error_code=ErrorCode.WECHAT_SYSTEM_ERROR)
                else:
                    raise APIError(error_code=ErrorCode.WECHAT_API_ERROR)

            # 获取成功，存入Redis
            access_token = result.get('access_token')
            expires_in = result.get('expires_in', 300)

            # 设置到Redis，提前30秒过期
            rdb.client.setex(redis_key, expires_in - 30, access_token)

            current_app.logger.info(f"成功获取并缓存微信access_token | 缓存时间:{expires_in - 30}秒")
            return access_token

        except APIError as e:
            raise e
        except requests.exceptions.Timeout as e:
            current_app.logger.warning(f"WECHAT_TOKEN_TIMEOUT 微信令牌接口超时: {str(e)}")
            raise RetryableError(error_code=ErrorCode.THIRD_PARTY_TIMEOUT) from e
        except requests.exceptions.RequestException as e:
            current_app.logger.warning(f"WECHAT_TOKEN_HTTP_ERROR 微信令牌接口异常: {str(e)}")
            raise APIError(error_code=ErrorCode.THIRD_PARTY_ERROR) from e
        except Exception as e:
            current_app.logger.exception("WECHAT_TOKEN_UNKNOWN_ERROR 未处理的微信令牌接口异常")
            raise APIError(error_code=ErrorCode.UNKNOWN_ERROR) from e

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type(RetryableError),
        reraise=True)
    def upload_shipping_info(self, transaction_id: str, is_all_delivered: bool,
                             shipping_list: list[dict], openid: str) -> dict:
        """
        发货信息录入接口

        Args:
            transaction_id: 微信支付订单号
            is_all_delivered: 是否全部发货
            shipping_list: 发货列表，包含发货商品信息
            openid: 用户openid

        Returns:
            dict: 接口返回结果
        """
        try:
            # 获取access_token
            access_token = self.get_stable_access_token()

            url = f"https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info?access_token={access_token}"

            # 准备请求数据
            data = {
                "order_key": {
                    "order_number_type": 2,  # 2表示微信支付订单号
                    "transaction_id": transaction_id
                },
                "logistics_type": 1,  # 1表示实体物流发货
                "delivery_mode": 2,  # 2表示分拆发货
                "is_all_delivered": is_all_delivered,  # 是否全部发货
                "shipping_list": [],
                "upload_time": datetime.now(ZoneInfo("Asia/Shanghai")).isoformat(timespec='milliseconds'),
                "payer": {
                    "openid": openid
                }
            }

            for item in shipping_list:
                data["shipping_list"].append({
                    "tracking_no": item["tracking_no"],
                    "express_company": self.express_code_mapping[item["express_company"]]["delivery_id"],
                    "item_desc": item["item_desc"],
                    "contact": {
                        "receiver_contact": item["contact_phone"]
                    }
                })

            # 记录请求信息
            current_app.logger.info(
                f"WECHAT_UPLOAD_SHIPPING | transaction_id:{transaction_id}, is_all_delivered:{is_all_delivered}"
            )

            # 发送请求
            response = self.session.post(url, json=data, timeout=10)
            response.raise_for_status()
            result = response.json()

            # 处理错误
            if 'errcode' in result and result['errcode'] != 0:
                errcode = result['errcode']
                errmsg = result.get('errmsg', '未知错误')

                current_app.logger.error(
                    f"WECHAT_UPLOAD_SHIPPING_ERROR | transaction_id:{transaction_id}, errcode:{errcode}, errmsg:{errmsg}"
                )

                # 根据错误码处理不同情况
                if errcode == -1 or errcode == 10060012 or errcode == 10060019:  # 系统繁忙
                    raise RetryableError(error_code=ErrorCode.WECHAT_SYSTEM_ERROR)
                else:
                    raise APIError(error_code=ErrorCode.WECHAT_API_ERROR, custom_message=f"微信发货接口错误: {errmsg}")

            current_app.logger.info(f"WECHAT_UPLOAD_SHIPPING_SUCCESS | transaction_id:{transaction_id}")
            return result

        except APIError as e:
            raise e
        except requests.exceptions.Timeout as e:
            current_app.logger.warning(
                f"WECHAT_UPLOAD_SHIPPING_TIMEOUT | transaction_id:{transaction_id}, error:{str(e)}")
            raise RetryableError(error_code=ErrorCode.THIRD_PARTY_TIMEOUT) from e
        except requests.exceptions.RequestException as e:
            current_app.logger.warning(
                f"WECHAT_UPLOAD_SHIPPING_HTTP_ERROR | transaction_id:{transaction_id}, error:{str(e)}")
            raise APIError(error_code=ErrorCode.THIRD_PARTY_ERROR) from e
        except Exception as e:
            current_app.logger.exception(f"WECHAT_UPLOAD_SHIPPING_UNKNOWN_ERROR | transaction_id:{transaction_id}")
            raise APIError(error_code=ErrorCode.UNKNOWN_ERROR) from e


class WeChatService:
    """微信服务扩展"""

    def __init__(self):
        self._client = None

    def init_app(self, app):
        """初始化微信客户端"""
        self._client = WeChatAPIClient(
            appid=app.config['WECHAT_APPID'],
            secret=app.config['WECHAT_SECRET']
        )

    @property
    def client(self):
        if not self._client:
            raise InternalServerError(custom_message="微信服务未初始化")
        return self._client


wechat = WeChatService()
