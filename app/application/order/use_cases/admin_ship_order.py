from typing import Any

from app.application.order.services.upload_shipping_to_wechat import upload_shipping_info
from app.domain.order.repositories import OrderRepository
from app.domain.order.services import OrderService
from app.domain.repositories import TransactionManager


class AdminShipOrderUseCase:
    """管理员发货用例"""

    def __init__(
            self,
            order_service: OrderService,
            order_repo: OrderRepository,
            db_ctx: TransactionManager
    ):
        self.order_service = order_service
        self.order_repo = order_repo
        self.db_ctx = db_ctx

    def execute(self, order_no: str, logistics_company: str, tracking_number: str,
                operator_id: int) -> dict[str, Any]:
        """执行管理员发货操作

        Args:
            order_no: 子订单号
            logistics_company: 物流公司编码
            tracking_number: 快递单号
            operator_id: 操作人ID（管理员ID）

        Returns:
            发货结果
        """
        # 检查订单号是否是子订单
        is_sub_order = '-' in order_no

        if is_sub_order:
            # 调用领域服务处理发货逻辑
            order = self.order_service.ship_sub_order(
                sub_order_no=order_no,
                logistics_company=logistics_company,
                tracking_number=tracking_number,
                operator_id=operator_id
            )
            with self.db_ctx:
                self.order_repo.save(order)

            # 向微信报送发货信息
            upload_shipping_info(order)

            return {}
        else:
            raise NotImplementedError("暂时不支持直接发货主订单")
