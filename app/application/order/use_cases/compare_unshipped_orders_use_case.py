from typing import Dict, Set, Any
from flask import current_app

from app.domain.order.repositories import OrderRepository
from app.domain.order.value_objects import SubOrderStatus
from app.infrastructure.payment.services import WechatPaymentService


class CompareUnshippedOrdersUseCase:
    """比较本地未发货订单与微信支付平台未发货订单用例"""

    def __init__(self, order_repo: OrderRepository, payment_service: WechatPaymentService):
        self.order_repo = order_repo
        self.payment_service = payment_service

    def execute(self) -> Dict[str, Any]:
        """执行比较未发货订单
        
        Returns:
            Dict: 包含比较结果的字典
                - is_consistent: bool, 是否一致
                - local_unshipped_count: int, 本地未发货订单数量
                - wechat_unshipped_count: int, 微信支付平台未发货订单数量
                - local_only: list, 仅在本地存在的微信支付ID
                - wechat_only: list, 仅在微信支付平台存在的微信支付ID
                - common: list, 两边都存在的微信支付ID
        """
        try:
            # 获取本地未发货订单的微信支付ID集合
            local_unshipped_payment_ids = self._get_local_unshipped_payment_ids()
            current_app.logger.info(f"本地未发货订单数量: {len(local_unshipped_payment_ids)}")
            
            # 获取微信支付平台未发货订单的微信支付ID集合
            wechat_unshipped_payment_ids = self._get_wechat_unshipped_payment_ids()
            current_app.logger.info(f"微信支付平台未发货订单数量: {len(wechat_unshipped_payment_ids)}")
            
            # 计算差异
            local_only = local_unshipped_payment_ids - wechat_unshipped_payment_ids
            wechat_only = wechat_unshipped_payment_ids - local_unshipped_payment_ids
            common = local_unshipped_payment_ids & wechat_unshipped_payment_ids
            
            # 判断是否一致
            is_consistent = len(local_only) == 0 and len(wechat_only) == 0
            
            current_app.logger.info(f"订单比较结果 - 一致性: {is_consistent}, 仅本地: {len(local_only)}, 仅微信: {len(wechat_only)}, 共同: {len(common)}")
            
            return {
                'is_consistent': is_consistent,
                'local_unshipped_count': len(local_unshipped_payment_ids),
                'wechat_unshipped_count': len(wechat_unshipped_payment_ids),
                'local_only': list(local_only),
                'wechat_only': list(wechat_only),
                'common': list(common)
            }
            
        except Exception as e:
            current_app.logger.error(f"比较未发货订单失败: {str(e)}")
            raise

    def _get_local_unshipped_payment_ids(self) -> Set[str]:
        """获取本地未发货订单的微信支付ID集合
        
        Returns:
            Set[str]: 微信支付交易号集合
        """
        try:
            # 获取所有状态为PAID（已支付待发货）的子订单
            unshipped_sub_orders = self.order_repo.find_sub_orders_by_status(SubOrderStatus.PAID)
            
            payment_ids = set()
            for sub_order in unshipped_sub_orders:
                # 通过子订单获取母订单
                parent_order = self.order_repo.find_by_id(sub_order.order_id)
                if parent_order and parent_order.payment_info and parent_order.payment_info.transaction_id:
                    payment_ids.add(parent_order.payment_info.transaction_id)
            
            current_app.logger.info(f"本地未发货子订单数量: {len(unshipped_sub_orders)}, 对应母订单微信支付ID数量: {len(payment_ids)}")
            return payment_ids
            
        except Exception as e:
            current_app.logger.error(f"获取本地未发货订单失败: {str(e)}")
            raise

    def _get_wechat_unshipped_payment_ids(self) -> Set[str]:
        """获取微信支付平台未发货订单的微信支付ID集合
        
        Returns:
            Set[str]: 微信支付交易号集合
        """
        try:
            # 调用微信支付平台获取未发货订单列表
            unshipped_orders = self.payment_service.get_unshipped_orders()
            
            payment_ids = set()
            for order in unshipped_orders:
                if order.get('transaction_id'):
                    payment_ids.add(order['transaction_id'])
            
            current_app.logger.info(f"微信支付平台未发货订单数量: {len(unshipped_orders)}, 微信支付ID数量: {len(payment_ids)}")
            return payment_ids
            
        except Exception as e:
            current_app.logger.error(f"获取微信支付平台未发货订单失败: {str(e)}")
            raise
