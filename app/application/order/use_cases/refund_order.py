from decimal import Decimal
from logging import Logger
from typing import Optional

from app.application.order.services.upload_shipping_to_wechat import upload_shipping_info
from app.domain.order.exceptions import OrderNotPaidError, OrderNotFoundError
from app.domain.order.repositories import OrderRepository
from app.domain.order.services import PaymentService, OrderService
from app.domain.order.value_objects import RefundReason, OrderStatus
from app.domain.repositories import TransactionManager


class RefundOrderUseCase:
    """订单退款用例"""

    def __init__(self,
                 order_repo: OrderRepository,
                 order_service: OrderService,
                 db_ctx: TransactionManager,
                 payment_service: PaymentService,
                 logger: Logger
                 ):
        self.order_repo = order_repo
        self.order_service = order_service
        self.db_ctx = db_ctx
        self.payment_service = payment_service
        self.logger = logger

    def execute(self, order_no: str, reason: RefundReason, operator_id: int, operator_type: str = "用户",
                refund_amount: Optional[Decimal] = None) -> dict:
        """执行订单退款操作

        Args:
            order_no: 订单号（可以是主订单号或子订单号）
            reason: 退款原因
            operator_id: 操作人ID
            operator_type: 操作人类型
            refund_amount: 退款金额（仅客服可指定，用户只能全额退款）

        Returns:
            退款是否成功
        """
        # 1. 判断是主订单还是子订单
        is_sub_order = '-' in order_no

        # 提取主订单号
        if is_sub_order:
            main_order_no, _ = order_no.split('-', 1)
        else:
            main_order_no = order_no

        # 2. 获取订单
        order = self.order_repo.find_by_order_no(main_order_no)
        if not order:
            raise OrderNotFoundError(f"订单 {main_order_no} 不存在")

        # 3. 检查订单状态
        if order.status != OrderStatus.PAID:
            raise OrderNotPaidError("未支付的订单不能退款")

        # 4. 处理退款
        if is_sub_order:
            self.order_service.sub_order_refund(order, order_no, reason, operator_id, operator_type,
                                                refund_amount)

            # 向微信报送发货信息
            try:
                if all(so.logistics_info for so in order.sub_orders):
                    upload_shipping_info(order)
                    self.logger.info(f"已向微信报送订单 {order.order_no} 的发货信息")
                else:
                    self.logger.info(f"订单 {order.order_no} 有子订单未发货，跳过报送发货信息")
            except Exception as e:
                self.logger.error(f"向微信报送发货信息失败: {str(e)}")

            return {}
        else:
            raise NotImplementedError("暂不允许订单整单退款")
