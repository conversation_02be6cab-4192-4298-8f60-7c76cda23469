from app.domain.order.aggregates import Order
from app.domain.order.value_objects import SubOrderStatus
from app.extensions import db
from app.models import Product, SKU
from app.models.user import UserAuth as UserAuthModel
from app.services import wechat


def upload_shipping_info(order: Order) -> None:
    """发货信息报送接口

    Args:
        order: 订单聚合根

    Returns:
        None
    """
    # 获取发货信息
    shipping_list = []
    for so in order.sub_orders:
        if so.logistics_info:
            product_title = db.session.execute(
                db.select(Product.title)
                .join(SKU, SKU.product_id == Product.id)
                .where(SKU.id == so.sku_id)
            ).scalar_one_or_none()
            phone = order.shipping_address.contact_phone

            shipping_list.append({
                "tracking_no": so.logistics_info.tracking_number,
                "express_company": so.logistics_info.logistics_company,
                "item_desc": product_title,
                "contact": {
                    "receiver_contact": phone[:3] + '****' + phone[-4:]
                }
            })

    # 获取用户openid
    openid = db.session.execute(
        db.select(UserAuthModel.auth_key).where(
            UserAuthModel.user_id == order.user_id,
            UserAuthModel.auth_type == 'wechat_openid'
        )
    ).scalar_one_or_none()

    # 向微信报送发货信息
    wechat.client.upload_shipping_info(
        transaction_id=order.payment_info.transaction_id,
        is_all_delivered=all(so.status != SubOrderStatus.PAID for so in order.sub_orders),
        shipping_list=shipping_list,
        openid=openid
    )
