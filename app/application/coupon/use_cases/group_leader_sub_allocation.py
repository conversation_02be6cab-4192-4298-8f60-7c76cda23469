from datetime import datetime, timed<PERSON><PERSON>
from decimal import Decimal
from typing import Optional

from app.domain.coupon.aggregates import GroupLeaderSubAllocation, GroupLeaderAllocation, CouponAllocation
from app.domain.coupon.exceptions import InsufficientInventoryError, UserNotInTargetGroupError, \
    InvalidAllocationLinkError, DailyLimitExceededError, UserLimitExceededError, HoldingLimitExceededError
from app.domain.coupon.repositories import GroupLeaderAllocationRepository, GroupLeaderSubAllocationRepository, \
    CouponAllocationRepository, UserCouponRepository
from app.domain.coupon.services import GroupLeaderAllocationService
from app.domain.coupon.value_objects import CouponStatus
from app.domain.user.repositories import GroupLeaderRepository, GroupMemberRepository, UserRepository
from app.extensions import db


class GetGroupLeaderAllocationsUseCase:
    def __init__(self, repo: GroupLeaderAllocationRepository):
        self.repo = repo

    @staticmethod
    def _format_decimal(value: Decimal) -> str:
        """格式化Decimal值，移除末尾的零和多余的小数点"""
        formatted = format(value.normalize(), 'f')
        if '.' in formatted:
            formatted = formatted.rstrip('0').rstrip('.')
        return formatted

    def execute(self, leader_id: int) -> list[dict]:
        """获取指定团长的所有二次分配"""
        # 一次性获取所有二次分配及关联的券分配聚合根
        allocations_with_coupons = self.repo.find_with_coupon_by_leader_id(leader_id)

        return [self._to_dto(alloc, coupon_alloc) for alloc, coupon_alloc in allocations_with_coupons]

    def _to_dto(self,
                allocation: GroupLeaderAllocation,
                coupon_alloc: CouponAllocation) -> dict:
        return {
            "id": allocation.id,
            "coupon_allocation_id": allocation.coupon_allocation_id,
            "total_quota": allocation.total_quota,
            "used_quota": allocation.used_quota,
            "remaining_quota": allocation.total_quota - allocation.used_quota,
            "coupon_title": coupon_alloc.title,
            "coupon_description": coupon_alloc.description,
            "discount_threshold": self._format_decimal(coupon_alloc.discount_rule.threshold),
            "discount_amount": self._format_decimal(coupon_alloc.discount_rule.discount_amount),
            "discount_type": coupon_alloc.discount_rule.discount_type.name.lower(),
            "validity_type": coupon_alloc.validity.validity_type.name.lower(),
            "validity_end": coupon_alloc.validity.end_time,
            "duration_days": coupon_alloc.validity.duration_days,
            "daily_limit": coupon_alloc.claim_limits.daily_limit,
            "user_limit": coupon_alloc.claim_limits.user_limit,
            "holding_limit": coupon_alloc.claim_limits.holding_limit
        }


class CreateGroupLeaderSubAllocationUseCase:
    """创建团长的三次分配"""

    def __init__(
            self,
            service: GroupLeaderAllocationService,
            allocation_repo: GroupLeaderAllocationRepository,
            sub_allocation_repo: GroupLeaderSubAllocationRepository,
            leader_repo: GroupLeaderRepository
    ):
        self.service = service
        self.allocation_repo = allocation_repo
        self.sub_allocation_repo = sub_allocation_repo
        self.leader_repo = leader_repo

    def execute(self, leader_id: int, leader_allocation_id: int, quantity: int) -> GroupLeaderSubAllocation:
        try:
            # 获取二次分配
            leader_alloc = self.allocation_repo.get(leader_allocation_id)

            # 验证团长所有权
            if leader_alloc.leader_id != leader_id:
                raise PermissionError("无权操作该分配")

            # 创建三次分配
            sub_alloc = self.service.create_sub_allocation(
                leader_id=leader_id,
                leader_allocation=leader_alloc,
                quantity=quantity
            )

            # 保存聚合根
            self.sub_allocation_repo.save(sub_alloc)
            self.allocation_repo.save(leader_alloc)

            # 提交事务
            db.session.commit()
            return sub_alloc
        except Exception as e:
            db.session.rollback()
            raise


class GetGroupLeaderSubAllocationsUseCase:
    def __init__(
            self,
            repo: GroupLeaderSubAllocationRepository,
            user_coupon_repo: UserCouponRepository,
            user_repo: UserRepository
    ):
        self.repo = repo
        self.user_coupon_repo = user_coupon_repo
        self.user_repo = user_repo

    def execute(self, leader_id: int) -> list[dict]:
        sub_allocations = self.repo.find_with_coupon_by_leader_id(leader_id)
        dtos = []
        for sub_alloc, leader_alloc, coupon_alloc in sub_allocations:
            # 获取用户信息
            user_ids = self.user_coupon_repo.get_user_ids_by_sub_allocation_token(sub_alloc.token)
            users = self.user_repo.get_users_by_ids(user_ids)
            user_info = [{"nickname": u.nickname, "avatar": u.avatar_url} for u in users]

            dto = self._to_dto(sub_alloc, coupon_alloc)
            dto["users"] = user_info  # 添加用户信息
            dtos.append(dto)
        return [dto for dto in dtos if dto is not None]

    @staticmethod
    def _format_decimal(value: Decimal) -> str:
        """格式化Decimal值，移除末尾的零和多余的小数点"""
        formatted = format(value.normalize(), 'f')
        if '.' in formatted:
            formatted = formatted.rstrip('0').rstrip('.')
        return formatted

    def _to_dto(self,
                sub_alloc: GroupLeaderSubAllocation,
                coupon_alloc: CouponAllocation) -> Optional[dict]:
        """三次分配及关联的优惠券信息为平铺结构"""
        current_time = datetime.now()
        allocation_end = coupon_alloc.distribution_auth.allocation_end_time

        # 状态判断
        if not sub_alloc.is_valid:
            status = "invalid"
        elif sub_alloc.inventory.remaining <= 0:
            status = "exhausted"
        elif allocation_end < current_time:
            status = "expired"
        else:
            status = "active"

        # 失效数据过滤
        cutoff_time = current_time - timedelta(days=90)

        if status in ("invalid", "exhausted", "expired"):
            if status == "expired":
                # 对于过期状态，使用分配结束时间判断
                expiration_time = coupon_alloc.distribution_auth.allocation_end_time
                if expiration_time < cutoff_time:
                    return None
            else:
                # 对于失效和用完状态，使用创建时间判断
                if sub_alloc.created_at < cutoff_time:
                    return None

        return {
            # 三次分配基础信息
            "sub_allocation_id": sub_alloc.id,
            "token": sub_alloc.token,
            "total_inventory": sub_alloc.inventory.total,
            "remaining_inventory": sub_alloc.inventory.remaining,
            "status": status,

            # 优惠券核心信息
            "coupon_title": coupon_alloc.title,
            "coupon_description": coupon_alloc.description,

            # 折扣规则
            "discount_threshold": self._format_decimal(coupon_alloc.discount_rule.threshold),
            "discount_amount": self._format_decimal(coupon_alloc.discount_rule.discount_amount),
            "discount_type": coupon_alloc.discount_rule.discount_type.name.lower(),

            # 有效期配置
            "validity_type": coupon_alloc.validity.validity_type.name.lower(),
            "validity_end": coupon_alloc.validity.end_time if coupon_alloc.validity.end_time else None,
            "duration_days": coupon_alloc.validity.duration_days,

            # 领取限制
            "daily_limit": coupon_alloc.claim_limits.daily_limit,
            "user_limit": coupon_alloc.claim_limits.user_limit,
            "holding_limit": coupon_alloc.claim_limits.holding_limit,

            # 关联的二次分配信息
            "leader_allocation_id": sub_alloc.leader_allocation_id,
            "coupon_allocation_id": coupon_alloc.id
        }


class InvalidateGroupLeaderSubAllocationUseCase:
    """失效团长三次分配用例"""

    def __init__(
            self,
            service: GroupLeaderAllocationService,
            allocation_repo: GroupLeaderAllocationRepository,
            sub_allocation_repo: GroupLeaderSubAllocationRepository,
    ):
        self.service = service
        self.allocation_repo = allocation_repo
        self.sub_allocation_repo = sub_allocation_repo

    def execute(self, leader_id: int, token: str) -> dict:
        try:
            # 获取三次分配
            sub_alloc = self.sub_allocation_repo.get_by_token(token)

            # 验证团长所有权
            if sub_alloc.leader_id != leader_id:
                raise PermissionError("无权操作该分配")

            # 获取关联的二次分配
            leader_alloc = self.allocation_repo.get(sub_alloc.leader_allocation_id)

            # 执行作废操作
            self.service.invalidate_sub_allocation(
                leader_alloc=leader_alloc,
                sub_alloc=sub_alloc
            )

            # 保存状态
            self.sub_allocation_repo.save(sub_alloc)
            self.allocation_repo.save(leader_alloc)

            # 提交事务
            db.session.commit()

            return {
                "token": sub_alloc.token,
                "remaining_quota": leader_alloc.total_quota - leader_alloc.used_quota
            }
        except Exception as e:
            db.session.rollback()
            raise


class GetGroupLeaderSubAllocationByTokenUseCase:
    """通过token获取三次分配详情用例"""

    def __init__(
            self,
            sub_allocation_repo: GroupLeaderSubAllocationRepository,
            leader_allocation_repo: GroupLeaderAllocationRepository,
            coupon_allocation_repo: CouponAllocationRepository
    ):
        self.sub_allocation_repo = sub_allocation_repo
        self.leader_allocation_repo = leader_allocation_repo
        self.coupon_allocation_repo = coupon_allocation_repo

    @staticmethod
    def _format_decimal(value: Decimal) -> str:
        """格式化Decimal为字符串，去除多余零"""
        return format(value.normalize(), 'f').rstrip('0').rstrip('.') if '.' in format(value.normalize(),
                                                                                       'f') else format(
            value.normalize(), 'f')

    def execute(self, token: str) -> dict:
        current_time = datetime.now()

        # 获取聚合根
        sub_alloc = self.sub_allocation_repo.get_by_token(token)
        leader_alloc = self.leader_allocation_repo.get(sub_alloc.leader_allocation_id)
        coupon_alloc = self.coupon_allocation_repo.get(leader_alloc.coupon_allocation_id)

        # 基础状态判断
        allocation_expired = coupon_alloc.distribution_auth.allocation_end_time < current_time
        validity_expired = coupon_alloc.validity.end_time < current_time if coupon_alloc.validity.end_time else False

        return {
            # 三次分配核心信息
            "token": sub_alloc.token,
            "is_valid": sub_alloc.is_valid,
            "total_inventory": sub_alloc.inventory.total,
            "remaining_inventory": sub_alloc.inventory.remaining,
            "created_at": sub_alloc.created_at,

            # 优惠券基础信息
            "coupon_title": coupon_alloc.title,
            "coupon_description": coupon_alloc.description,

            # 折扣规则（平铺）
            "discount_threshold": self._format_decimal(coupon_alloc.discount_rule.threshold),
            "discount_amount": self._format_decimal(coupon_alloc.discount_rule.discount_amount),
            "discount_type": coupon_alloc.discount_rule.discount_type.name.lower(),

            # 有效期配置（平铺）
            "validity_type": coupon_alloc.validity.validity_type.name.lower(),
            "validity_start": coupon_alloc.validity.start_time if coupon_alloc.validity.start_time else None,
            "validity_end": coupon_alloc.validity.end_time if coupon_alloc.validity.end_time else None,
            "duration_days": coupon_alloc.validity.duration_days,

            # 领取限制
            "daily_limit": coupon_alloc.claim_limits.daily_limit,
            "user_limit": coupon_alloc.claim_limits.user_limit,
            "holding_limit": coupon_alloc.claim_limits.holding_limit,

            # 状态综合判断
            "status": self._determine_status(sub_alloc, allocation_expired, validity_expired),

            # 关联的分配信息
            "leader_allocation_id": sub_alloc.leader_allocation_id,
            "coupon_allocation_id": coupon_alloc.id
        }

    def _determine_status(self, sub_alloc, allocation_expired, validity_expired):
        """综合判断状态"""
        if not sub_alloc.is_valid:
            return "invalid"
        if sub_alloc.inventory.remaining <= 0:
            return "exhausted"
        if allocation_expired:
            return "allocation_expired"
        if validity_expired:
            return "validity_expired"
        return "active"


class ClaimCouponBySubAllocationTokenUseCase:
    """通过三次分配token领取优惠券用例"""

    def __init__(
            self,
            sub_alloc_repo: GroupLeaderSubAllocationRepository,
            leader_alloc_repo: GroupLeaderAllocationRepository,
            coupon_alloc_repo: CouponAllocationRepository,
            user_coupon_repo: UserCouponRepository,
            group_member_repo: GroupMemberRepository,
            db_session
    ):
        self.sub_alloc_repo = sub_alloc_repo
        self.leader_alloc_repo = leader_alloc_repo
        self.coupon_alloc_repo = coupon_alloc_repo
        self.user_coupon_repo = user_coupon_repo
        self.group_member_repo = group_member_repo
        self.db_session = db_session

    def execute(self, token: str, user_id: int) -> dict:
        try:
            # 获取三次分配
            sub_alloc = self.sub_alloc_repo.get_by_token(token)

            # 获取三次分配对应的团长ID
            allocation_leader_id = sub_alloc.leader_id
            # 查询当前用户所属的团长ID
            user_leader_id = self.group_member_repo.get_leader_id_by_member_id(user_id)
            # 验证用户是否属于该团长团队
            if user_leader_id != allocation_leader_id:
                raise UserNotInTargetGroupError("用户不属于该团长团队，无法领取优惠券")

            # 验证分配有效性
            if not sub_alloc.is_valid:
                raise InvalidAllocationLinkError("该分配链接已失效")
            if sub_alloc.inventory.remaining <= 0:
                raise InsufficientInventoryError("优惠券已领完")

            # 获取关联的二次分配
            leader_alloc = self.leader_alloc_repo.get(sub_alloc.leader_allocation_id)

            # 获取原始券分配
            coupon_alloc = self.coupon_alloc_repo.get(leader_alloc.coupon_allocation_id)

            # 获取用户优惠券集合并检查领取限制
            user_collection = self.user_coupon_repo.get(user_id)
            status = user_collection.get_claim_status(coupon_alloc, datetime.now())
            if status == CouponStatus.EXPIRED:
                raise InvalidAllocationLinkError("该分配链接已失效")
            if status == CouponStatus.OUT_OF_STOCK:
                raise InsufficientInventoryError("优惠券已领完")
            if status == CouponStatus.DAILY_LIMIT:
                raise DailyLimitExceededError("用户领取次数已达上限")
            if status == CouponStatus.USER_LIMIT:
                raise UserLimitExceededError("用户领取次数已达上限")
            if status == CouponStatus.HOLDING_LIMIT:
                raise HoldingLimitExceededError("用户持有优惠券已达上限")

            # 执行库存扣减
            sub_alloc.inventory.consume(1)
            coupon_alloc.inventory.consume(1)

            # 创建用户优惠券
            user_coupon = user_collection.add_coupon(coupon_alloc, sub_allocation_token=sub_alloc.token)

            # 保存变更
            self.sub_alloc_repo.save(sub_alloc)
            self.leader_alloc_repo.save(leader_alloc)
            self.coupon_alloc_repo.save(coupon_alloc)
            self.user_coupon_repo.save(user_collection)

            self.db_session.commit()

            return {}
        except Exception as e:
            self.db_session.rollback()
            raise


class GetAllGroupLeaderSubAllocationsForAdminUseCase:
    """管理员获取所有团长三次分配信息用例"""

    def __init__(
            self,
            sub_alloc_repo: GroupLeaderSubAllocationRepository,
            leader_alloc_repo: GroupLeaderAllocationRepository,
            coupon_alloc_repo: CouponAllocationRepository,
            user_coupon_repo: UserCouponRepository,
            user_repo: UserRepository
    ):
        self.sub_alloc_repo = sub_alloc_repo
        self.leader_alloc_repo = leader_alloc_repo
        self.coupon_alloc_repo = coupon_alloc_repo
        self.user_coupon_repo = user_coupon_repo
        self.user_repo = user_repo

    @staticmethod
    def _format_decimal(value: Decimal) -> str:
        """格式化Decimal为字符串，去除多余零"""
        formatted = format(value.normalize(), 'f')
        if '.' in formatted:
            formatted = formatted.rstrip('0').rstrip('.')
        return formatted

    def execute(self) -> list[dict]:
        """获取所有团长三次分配信息"""
        # 获取所有三次分配
        sub_allocations = self.sub_alloc_repo.find_all()
        if not sub_allocations:
            return []

        # 获取所有二次分配ID
        leader_allocation_ids = [sa.leader_allocation_id for sa in sub_allocations]
        leader_allocations = {}
        for batch in [leader_allocation_ids[i:i + 100] for i in range(0, len(leader_allocation_ids), 100)]:
            batch_allocations = self.leader_alloc_repo.find_by_ids(batch)
            for la in batch_allocations:
                leader_allocations[la.id] = la

        # 获取所有一次分配ID
        coupon_allocation_ids = [la.coupon_allocation_id for la in leader_allocations.values()]
        coupon_allocations = {}
        for batch in [coupon_allocation_ids[i:i + 100] for i in range(0, len(coupon_allocation_ids), 100)]:
            batch_allocations = self.coupon_alloc_repo.find_by_ids(batch)
            for ca in batch_allocations:
                coupon_allocations[ca.id] = ca

        # 获取所有团长ID
        leader_ids = [sa.leader_id for sa in sub_allocations]
        leaders = {}
        for batch in [leader_ids[i:i + 100] for i in range(0, len(leader_ids), 100)]:
            batch_leaders = self.user_repo.get_users_by_ids(batch)
            for leader in batch_leaders:
                leaders[leader.id] = leader

        # 构建结果
        result = []
        for sub_alloc in sub_allocations:
            # 获取关联的二次分配
            leader_alloc = leader_allocations.get(sub_alloc.leader_allocation_id)
            if not leader_alloc:
                continue

            # 获取关联的一次分配
            coupon_alloc = coupon_allocations.get(leader_alloc.coupon_allocation_id)
            if not coupon_alloc:
                continue

            # 获取团长信息
            leader = leaders.get(sub_alloc.leader_id)
            if not leader:
                continue

            # 获取用户领取记录
            user_coupons = self.user_coupon_repo.find_by_sub_allocation_token(sub_alloc.token)
            claimed_count = len(user_coupons) if user_coupons else 0

            # 构建用户列表
            user_list = []
            i = 1
            if user_coupons:
                # 获取所有用户ID
                user_ids = [uc[1] for uc in user_coupons]
                users = {}
                for batch in [user_ids[i:i + 100] for i in range(0, len(user_ids), 100)]:
                    batch_users = self.user_repo.get_users_by_ids(batch)
                    for user in batch_users:
                        users[user.id] = user

                # 构建用户领取记录
                for uc, user_id in user_coupons:
                    user = users.get(user_id)
                    if not user:
                        continue

                    # 判断是否使用
                    is_used = False
                    used_time = None

                    if uc.usage_records and not uc.usage_records[-1].reverted:
                        is_used = True
                        used_time = uc.usage_records[-1].used_time

                    user_list.append({
                        "no": i,
                        "nickname": user.nickname,
                        "is_used": "●" if is_used else None,
                        "obtained_time": uc.obtained_time.strftime("%Y-%m-%d %H:%M:%S"),
                        "used_time": used_time.strftime("%Y-%m-%d %H:%M:%S") if used_time else "-"
                    })
                    i += 1

            # 获取优惠券类型（转换为小写枚举名）
            coupon_type = coupon_alloc.discount_rule.discount_type.name.lower()

            result.append({
                "leader": {
                    "nickname": leader.nickname,
                    "avatar_url": leader.avatar_url
                },
                "coupon": {
                    "id": coupon_alloc.id,
                    "type": coupon_type,
                    "title": coupon_alloc.title,
                    "description": coupon_alloc.description,
                    "discount_amount": float(coupon_alloc.discount_rule.discount_amount)
                },
                "sub_allocation": {
                    "id": sub_alloc.id,
                    "token": sub_alloc.token,
                    "created_at": sub_alloc.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "is_valid": sub_alloc.is_valid,
                    "claimed_count": claimed_count,
                    "total_count": sub_alloc.inventory.total
                },
                "user_list": user_list
            })

        return result
