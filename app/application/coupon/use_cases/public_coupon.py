from datetime import datetime
from decimal import Decimal
from typing import List, Dict, Any

from app.domain.coupon.aggregates import UserCouponItem
from app.domain.coupon.repositories import CouponAllocationRepository, UserCouponRepository
from app.domain.user.repositories import UserRepository


class GetPublicCouponsForAdminUseCase:
    """管理员获取公开优惠券信息用例"""

    def __init__(
            self,
            coupon_alloc_repo: CouponAllocationRepository,
            user_coupon_repo: UserCouponRepository,
            user_repo: UserRepository
    ):
        self.coupon_alloc_repo = coupon_alloc_repo
        self.user_coupon_repo = user_coupon_repo
        self.user_repo = user_repo

    @staticmethod
    def _format_decimal(value: Decimal) -> str:
        """格式化Decimal为字符串，去除多余零"""
        formatted = format(value.normalize(), 'f')
        if '.' in formatted:
            formatted = formatted.rstrip('0').rstrip('.')
        return formatted

    def execute(self) -> List[Dict[str, Any]]:
        """获取所有公开优惠券信息"""
        # 获取所有公开渠道的优惠券分配
        current_time = datetime.now()
        public_allocations = self.coupon_alloc_repo.find_public_allocations(current_time)

        if not public_allocations:
            return []

        result = []
        for coupon_alloc in public_allocations:
            # 获取该优惠券的所有用户领取记录
            user_coupons = self._get_user_coupons_by_allocation(coupon_alloc.id)

            # 构建用户列表
            user_list = self._build_user_list(user_coupons)

            # 获取优惠券类型（转换为小写枚举名）
            coupon_type = coupon_alloc.discount_rule.discount_type.name.lower()

            # 计算已领取数量
            claimed_count = len(user_coupons)

            result.append({
                "coupon": {
                    "type": coupon_type,
                    "title": coupon_alloc.title,
                    "description": coupon_alloc.description,
                    "discount_amount": self._format_decimal(coupon_alloc.discount_rule.discount_amount)
                },
                "statistics": {
                    "claimed_count": claimed_count,
                    "total_count": coupon_alloc.inventory.total,
                    "is_claimable": current_time < coupon_alloc.distribution_auth.allocation_end_time
                },
                "user_list": user_list
            })

        return result

    def _get_user_coupons_by_allocation(self, allocation_id: int) -> List[tuple[UserCouponItem, int]]:
        """获取指定优惠券分配的所有用户领取记录"""
        return self.user_coupon_repo.find_by_coupon_allocation_id(allocation_id)

    def _build_user_list(self, user_coupons: List[tuple[UserCouponItem, int]]) -> List[Dict[str, Any]]:
        """构建用户领取列表"""
        if not user_coupons:
            return []

        # 获取所有用户ID
        user_ids = [user_id for _, user_id in user_coupons]
        users = {}

        # 批量获取用户信息
        for batch in [user_ids[i:i + 100] for i in range(0, len(user_ids), 100)]:
            batch_users = self.user_repo.get_users_by_ids(batch)
            for user in batch_users:
                users[user.id] = user

        # 构建用户领取记录
        user_list = []
        i = 1
        for uc, user_id in user_coupons:
            user = users.get(user_id)
            if not user:
                continue

            # 判断是否使用
            is_used = False
            used_time = None

            if uc.usage_records and not uc.usage_records[-1].reverted:
                is_used = True
                used_time = uc.usage_records[-1].used_time

            user_list.append({
                "no": i,
                "nickname": user.nickname,
                "is_used": "●" if is_used else None,
                "obtained_time": uc.obtained_time.strftime("%Y-%m-%d %H:%M:%S"),
                "used_time": used_time.strftime("%Y-%m-%d %H:%M:%S") if used_time else "-"
            })
            i += 1

        return user_list
