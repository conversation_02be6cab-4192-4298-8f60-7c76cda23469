from datetime import datetime
from typing import Optional

from sqlalchemy import select

from app.domain.coupon.aggregates import CouponAllocation, UserCouponCollection, UserCouponItem, \
    GroupLeaderAllocation, GroupLeaderSubAllocation, PublicGroupLeaderAllocation
from app.domain.coupon.repositories import CouponAllocationRepository, UserCouponRepository, \
    GroupLeaderAllocationRepository, GroupLeaderSubAllocationRepository, PublicGroupLeaderAllocationRepository
from app.domain.coupon.value_objects import ValidityType, DistributionChannel, UsagePolicy, ApplicableScope, \
    DiscountRule, CouponType, ValidityPeriod, Inventory, DistributionAuthorization, ClaimLimits, UsageRecord
from app.extensions import db
from app.infrastructure.coupon.models import CouponAllocationModel, UserCouponModel, GroupLeaderAllocationModel, \
    GroupLeaderSubAllocationModel, PublicGroupLeaderAllocationModel
from app.utils.errors import OptimisticLockError


class SQLCouponAllocationRepository(CouponAllocationRepository):
    def __init__(self):
        self.model = CouponAllocationModel

    def get(self, id_: int) -> CouponAllocation:
        model = db.session.get(CouponAllocationModel, id_)
        if not model:
            raise ValueError(f"CouponAllocation {id_} not found")
        return self._from_orm(model)

    def find_by_ids(self, ids: list[int]) -> list[CouponAllocation]:
        if not ids:
            return []

        # 使用in_查询优化批量获取
        result = db.session.execute(
            select(CouponAllocationModel)
            .where(CouponAllocationModel.id.in_(ids))
        )
        models = result.scalars().all()
        return [self._from_orm(model) for model in models]

    def find_paginated(self, page: int, per_page: int) -> tuple[list[CouponAllocation], int]:
        query = self.model.query.order_by(self.model.id.desc())
        paginated = query.paginate(page=page, per_page=per_page, error_out=False)
        return [self._from_orm(item) for item in paginated.items], paginated.total

    def find_paginated_with_status(self, page: int, per_page: int, status: str) -> tuple[list[CouponAllocation], int]:
        """根据状态分页查询优惠券分配配置
        
        按照筛选细节实现：
        - 指定为无库存状态时，应该屏蔽掉已过指定使用时间和已过领取时间
        - 已过指定使用时间无需过滤已过领取时间和无库存
        - 已过领取时间无需过滤已过指定使用时间和无库存
        """
        now = datetime.now()

        # 构建基础查询
        query = self.model.query

        # 根据状态添加过滤条件
        if status:
            if status == 'normal':
                # 正常状态：未过期、未过领取时间、有库存
                query = query.filter(
                    self.model.allocation_end > now,
                    self.model.remaining_inventory > 0
                ).filter(
                    db.or_(
                        self.model.validity_type != ValidityType.FIXED_DATE.value,
                        self.model.validity_end > now
                    )
                )
            elif status == 'unefficacy':
                # 已过领取时间（不需要过滤已过期和无库存的情况）
                query = query.filter(
                    self.model.allocation_end <= now
                )
            elif status == 'expired':
                # 已过期（不需要过滤已过领取时间和无库存的情况）
                query = query.filter(
                    self.model.validity_type == ValidityType.FIXED_DATE.value,
                    self.model.validity_end <= now
                )
            elif status == 'unstock':
                # 无库存（需要屏蔽已过期和已过领取时间的情况）
                query = query.filter(
                    self.model.remaining_inventory <= 0,
                    self.model.allocation_end > now
                ).filter(
                    db.or_(
                        self.model.validity_type != ValidityType.FIXED_DATE.value,
                        self.model.validity_end > now
                    )
                )

        # 按ID倒序排序并分页
        query = query.order_by(self.model.id.desc())
        paginated = query.paginate(page=page, per_page=per_page, error_out=False)
        return [self._from_orm(item) for item in paginated.items], paginated.total

    def find_public_allocations(self, current_time: datetime) -> list[CouponAllocation]:
        """获取公开渠道且在当前分配时间窗口内的优惠券分配"""
        result = db.session.execute(
            select(self.model)
            .where(self.model.distribution_channel == DistributionChannel.PUBLIC.value,
                   self.model.allocation_start <= current_time,
                   self.model.allocation_end >= current_time)
            .order_by(self.model.created_at.desc())  # 排序语法保持不变
        )

        return [self._from_orm(model) for model in result.scalars()]

    def find_leader_channel_allocations(self, current_time: datetime) -> list[CouponAllocation]:
        """获取团长渠道且未过最晚可发放时间的优惠券分配"""
        result = db.session.execute(
            select(self.model)
            .where(self.model.distribution_channel == DistributionChannel.GROUP_LEADER.value,
                   self.model.allocation_end >= current_time)
            .order_by(self.model.created_at.desc())
        )

        return [self._from_orm(model) for model in result.scalars()]

    def save(self, entity: CouponAllocation) -> None:
        try:
            if entity.id is None:
                model = self._to_orm(entity)
                db.session.add(model)
                db.session.flush()
                entity.id = model.id
            else:
                model = db.session.get(self.model, entity.id, with_for_update=True)
                if model.version != entity.version:
                    raise OptimisticLockError("优惠券分配数据已被其他操作更新，请重试")
                self._update_orm(model, entity)
                entity.version += 1

            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise

    @staticmethod
    def _from_orm(model: CouponAllocationModel) -> CouponAllocation:
        return CouponAllocation(
            id=model.id,
            title=model.title,
            description=model.description,
            usage_policy=UsagePolicy(
                applicable_scope=ApplicableScope(model.applicable_product_ids)
            ),
            discount_rule=DiscountRule(
                threshold=model.threshold,
                discount_amount=model.discount_amount,
                discount_type=CouponType(model.discount_type)
            ),
            validity=ValidityPeriod(
                validity_type=ValidityType(model.validity_type),
                start_time=model.validity_start,
                end_time=model.validity_end,
                duration_days=model.duration_days
            ),
            inventory=Inventory(
                total=model.total_inventory,
                remaining=model.remaining_inventory
            ),
            distribution_auth=DistributionAuthorization(
                channel=DistributionChannel(model.distribution_channel),
                allocation_start_time=model.allocation_start,
                allocation_end_time=model.allocation_end
            ),
            claim_limits=ClaimLimits(
                daily_limit=model.daily_limit,
                user_limit=model.user_limit,
                holding_limit=model.holding_limit
            ),
            created_at=model.created_at,
            created_by=model.created_by,
            version=model.version
        )

    def _to_orm(self, entity: CouponAllocation) -> CouponAllocationModel:
        return CouponAllocationModel(
            title=entity.title,
            description=entity.description,
            applicable_product_ids=entity.usage_policy.applicable_scope.product_ids,
            threshold=entity.discount_rule.threshold,
            discount_amount=entity.discount_rule.discount_amount,
            discount_type=entity.discount_rule.discount_type.value,
            validity_type=entity.validity.validity_type.value,
            validity_start=entity.validity.start_time,
            validity_end=entity.validity.end_time,
            duration_days=entity.validity.duration_days,
            total_inventory=entity.inventory.total,
            remaining_inventory=entity.inventory.remaining,
            distribution_channel=entity.distribution_auth.channel.value,
            allocation_start=entity.distribution_auth.allocation_start_time,
            allocation_end=entity.distribution_auth.allocation_end_time,
            daily_limit=entity.claim_limits.daily_limit,
            user_limit=entity.claim_limits.user_limit,
            holding_limit=entity.claim_limits.holding_limit,
            created_at=entity.created_at,
            created_by=entity.created_by,
            version=entity.version
        )

    def _update_orm(self, model: CouponAllocationModel, entity: CouponAllocation):
        model.title = entity.title
        model.description = entity.description
        model.applicable_product_ids = entity.usage_policy.applicable_scope.product_ids
        model.threshold = entity.discount_rule.threshold
        model.discount_amount = entity.discount_rule.discount_amount
        model.discount_type = entity.discount_rule.discount_type.value
        model.validity_type = entity.validity.validity_type.value
        model.validity_start = entity.validity.start_time
        model.validity_end = entity.validity.end_time
        model.duration_days = entity.validity.duration_days
        model.total_inventory = entity.inventory.total
        model.remaining_inventory = entity.inventory.remaining
        model.distribution_channel = entity.distribution_auth.channel.value
        model.allocation_start = entity.distribution_auth.allocation_start_time
        model.allocation_end = entity.distribution_auth.allocation_end_time
        model.daily_limit = entity.claim_limits.daily_limit
        model.user_limit = entity.claim_limits.user_limit
        model.holding_limit = entity.claim_limits.holding_limit
        model.version = entity.version


class SQLUserCouponRepository(UserCouponRepository):
    def get(self, user_id: int) -> UserCouponCollection:
        # 使用 select 构建查询语句
        stmt = select(UserCouponModel).where(UserCouponModel.user_id == user_id)

        # 执行查询并获取结果
        result = db.session.execute(stmt)
        models = result.scalars().all()

        # 将 ORM 模型转换为领域对象
        coupons = [self._from_orm(model) for model in models]

        # 返回领域对象集合
        return UserCouponCollection(user_id=user_id, coupons=coupons)

    def get_user_ids_by_sub_allocation_token(self, token: str) -> list[int]:
        stmt = select(UserCouponModel.user_id).where(
            UserCouponModel.sub_allocation_token == token
        ).distinct()
        result = db.session.execute(stmt)
        return result.scalars().all()

    def find_by_sub_allocation_token(self, token: str) -> list[tuple[UserCouponItem, int]]:
        """根据三次分配token获取用户优惠券列表"""
        stmt = select(UserCouponModel).where(
            UserCouponModel.sub_allocation_token == token
        )
        result = db.session.execute(stmt)
        models = result.scalars().all()
        return [(self._from_orm(model), model.user_id) for model in models]

    def find_by_coupon_allocation_id(self, allocation_id: int) -> list[tuple[UserCouponItem, int]]:
        """根据优惠券分配ID获取用户优惠券列表"""
        stmt = select(UserCouponModel).where(
            UserCouponModel.coupon_allocation_id == allocation_id
        )
        result = db.session.execute(stmt)
        models = result.scalars().all()
        return [(self._from_orm(model), model.user_id) for model in models]

    def save(self, collection: UserCouponCollection):
        for coupon in collection.coupons:
            if coupon.id is None:
                model = self._to_orm(coupon, collection.user_id)
                db.session.add(model)
                db.session.flush()
                coupon.id = model.id
            else:
                model = db.session.get(UserCouponModel, coupon.id)
                self._update_orm(model, coupon)

    def _from_orm(self, model: UserCouponModel) -> UserCouponItem:
        return UserCouponItem(
            id=model.id,
            coupon_allocation_id=model.coupon_allocation_id,
            obtained_time=model.obtained_time,
            start_time=model.start_time,
            end_time=model.end_time,
            usage_records=[
                UsageRecord(
                    order_id=r['order_id'],
                    used_time=datetime.fromisoformat(r['used_time']),
                    reverted=r['reverted']
                ) for r in model.usage_records
            ],
            sub_allocation_token=model.sub_allocation_token,
            version=model.version
        )

    def _to_orm(self, entity: UserCouponItem, user_id: int) -> UserCouponModel:
        return UserCouponModel(
            user_id=user_id,
            coupon_allocation_id=entity.coupon_allocation_id,
            obtained_time=entity.obtained_time,
            start_time=entity.start_time,
            end_time=entity.end_time,
            usage_records=[
                {
                    'order_id': r.order_id,
                    'used_time': r.used_time.isoformat(),
                    'reverted': r.reverted
                } for r in entity.usage_records
            ],
            sub_allocation_token=entity.sub_allocation_token,
            version=entity.version
        )

    def _update_orm(self, model: UserCouponModel, entity: UserCouponItem):
        model.coupon_allocation_id = entity.coupon_allocation_id
        model.obtained_time = entity.obtained_time
        model.start_time = entity.start_time
        model.end_time = entity.end_time
        model.usage_records = [
            {
                'order_id': r.order_id,
                'used_time': r.used_time.isoformat(),
                'reverted': r.reverted
                } for r in entity.usage_records
            ]
        model.sub_allocation_token = entity.sub_allocation_token


class SQLGroupLeaderAllocationRepository(GroupLeaderAllocationRepository):
    def __init__(self):
        self.model = GroupLeaderAllocationModel

    def save_all(self, entities: list[GroupLeaderAllocation]):
        pass

    def get(self, id_: int) -> GroupLeaderAllocation:
        model = db.session.get(self.model, id_)
        if not model:
            raise ValueError(f"GroupLeaderAllocation {id_} not found")
        return self._from_orm(model)

    def find_by_leader_id(self, leader_id: int) -> list[GroupLeaderAllocation]:
        models = self.model.query.filter_by(leader_id=leader_id).all()
        return [self._from_orm(model) for model in models]

    def find_with_coupon_by_leader_id(self, leader_id: int) -> list[tuple[GroupLeaderAllocation, CouponAllocation]]:
        """获取团长二次分配及关联的完整券分配信息"""
        # 执行JOIN查询，获取GroupLeaderAllocationModel和完整的CouponAllocationModel
        query = (
            db.session.query(GroupLeaderAllocationModel, CouponAllocationModel)
            .join(
                CouponAllocationModel,
                GroupLeaderAllocationModel.coupon_allocation_id == CouponAllocationModel.id
            )
            .filter(GroupLeaderAllocationModel.leader_id == leader_id)
        )

        coupon_alloc_repo = SQLCouponAllocationRepository()
        results = []
        for alloc_model, coupon_model in query.all():
            # 转换GroupLeaderAllocation聚合根
            group_alloc = self._from_orm(alloc_model)
            # 转换CouponAllocation聚合根
            coupon_alloc = coupon_alloc_repo._from_orm(coupon_model)
            results.append((group_alloc, coupon_alloc))

        return results

    def find_by_leader_and_coupon(self, leader_id: int, coupon_allocation_id: int) -> Optional[GroupLeaderAllocation]:
        model = self.model.query.filter_by(
            leader_id=leader_id,
            coupon_allocation_id=coupon_allocation_id
        ).first()
        return self._from_orm(model) if model else None

    def find_by_ids(self, ids: list[int]) -> list[GroupLeaderAllocation]:
        """批量获取指定ID的二次分配"""
        if not ids:
            return []
        models = self.model.query.filter(self.model.id.in_(ids)).all()
        return [self._from_orm(model) for model in models]

    def save(self, entity: GroupLeaderAllocation):
        try:
            if entity.id is None:
                model = self._to_orm(entity)
                db.session.add(model)
                db.session.flush()
                entity.id = model.id
            else:
                model = db.session.get(self.model, entity.id, with_for_update=True)
                if model.version != entity.version:
                    raise OptimisticLockError("优惠券分配数据已被其他操作更新，请重试")
                entity.version += 1
                self._update_orm(model, entity)

        except Exception as e:
            db.session.rollback()
            raise

    @staticmethod
    def _from_orm(model: GroupLeaderAllocationModel) -> GroupLeaderAllocation:
        return GroupLeaderAllocation(
            id=model.id,
            created_at=model.created_at,
            created_by=model.created_by,
            updated_by=model.updated_by,
            version=model.version,
            coupon_allocation_id=model.coupon_allocation_id,
            leader_id=model.leader_id,
            total_quota=model.total_quota,
            used_quota=model.used_quota
        )

    @staticmethod
    def _to_orm(entity: GroupLeaderAllocation) -> GroupLeaderAllocationModel:
        return GroupLeaderAllocationModel(
            coupon_allocation_id=entity.coupon_allocation_id,
            leader_id=entity.leader_id,
            total_quota=entity.total_quota,
            used_quota=entity.used_quota,
            created_at=entity.created_at,
            created_by=entity.created_by,
            updated_by=entity.updated_by,
            version=entity.version
        )

    @staticmethod
    def _update_orm(model: GroupLeaderAllocationModel, entity: GroupLeaderAllocation):
        model.total_quota = entity.total_quota
        model.used_quota = entity.used_quota
        model.updated_by = entity.updated_by
        model.version = entity.version


class SQLGroupLeaderSubAllocationRepository(GroupLeaderSubAllocationRepository):
    def __init__(self):
        self.model = GroupLeaderSubAllocationModel

    def get_by_token(self, token: str) -> GroupLeaderSubAllocation:
        model = self.model.query.filter_by(token=token).first()
        if not model:
            raise ValueError(f"SubAllocation with token {token} not found")
        return self._from_orm(model)

    def find_all(self) -> list[GroupLeaderSubAllocation]:
        """获取所有三次分配信息"""
        models = self.model.query.all()
        return [self._from_orm(model) for model in models]

    def find_by_leader_id(self, leader_id: int) -> list[GroupLeaderSubAllocation]:
        models = self.model.query.filter_by(leader_id=leader_id).all()
        return [self._from_orm(model) for model in models]

    def find_with_coupon_by_leader_id(self, leader_id: int) -> list[
            tuple[GroupLeaderSubAllocation, GroupLeaderAllocation, CouponAllocation]]:
        """获取三次分配及关联的券分配信息"""
        # 执行三表关联查询
        query = (
            db.session.query(
                GroupLeaderSubAllocationModel,
                GroupLeaderAllocationModel,
                CouponAllocationModel
            )
            .join(
                GroupLeaderAllocationModel,
                GroupLeaderSubAllocationModel.leader_allocation_id == GroupLeaderAllocationModel.id
            )
            .join(
                CouponAllocationModel,
                GroupLeaderAllocationModel.coupon_allocation_id == CouponAllocationModel.id
            )
            .filter(GroupLeaderSubAllocationModel.leader_id == leader_id)
        )

        # 初始化其他仓储用于转换聚合根
        leader_alloc_repo = SQLGroupLeaderAllocationRepository()
        coupon_alloc_repo = SQLCouponAllocationRepository()

        results = []
        for sub_alloc_model, leader_alloc_model, coupon_model in query.all():
            # 转换各模型为聚合根
            sub_alloc = self._from_orm(sub_alloc_model)
            leader_alloc = leader_alloc_repo._from_orm(leader_alloc_model)
            coupon_alloc = coupon_alloc_repo._from_orm(coupon_model)
            results.append((sub_alloc, leader_alloc, coupon_alloc))

        return results

    def save(self, entity: GroupLeaderSubAllocation):
        try:
            if entity.id is None:
                model = self._to_orm(entity)
                db.session.add(model)
                db.session.flush()
                entity.id = model.id
            else:
                model = db.session.get(self.model, entity.id, with_for_update=True)
                if model.version != entity.version:
                    raise OptimisticLockError("优惠券分配数据已被其他操作更新，请重试")
                self._update_orm(model, entity)
                entity.version += 1

        except Exception as e:
            db.session.rollback()
            raise

    @staticmethod
    def _from_orm(model: GroupLeaderSubAllocationModel) -> GroupLeaderSubAllocation:
        return GroupLeaderSubAllocation(
            id=model.id,
            created_at=model.created_at,
            version=model.version,
            leader_id=model.leader_id,
            token=model.token,
            leader_allocation_id=model.leader_allocation_id,
            inventory=Inventory(
                total=model.total_inventory,
                remaining=model.remaining_inventory
            ),
            is_valid=model.is_valid
        )

    @staticmethod
    def _to_orm(entity: GroupLeaderSubAllocation) -> GroupLeaderSubAllocationModel:
        return GroupLeaderSubAllocationModel(
            leader_allocation_id=entity.leader_allocation_id,
            leader_id=entity.leader_id,
            token=entity.token,
            total_inventory=entity.inventory.total,
            remaining_inventory=entity.inventory.remaining,
            is_valid=entity.is_valid,
            created_at=entity.created_at,
            version=entity.version
        )

    @staticmethod
    def _update_orm(model: GroupLeaderSubAllocationModel, entity: GroupLeaderSubAllocation):
        model.remaining_inventory = entity.inventory.remaining
        model.is_valid = entity.is_valid
        model.version = entity.version


class SQLPublicGroupLeaderAllocationRepository(PublicGroupLeaderAllocationRepository):
    def __init__(self):
        self.model = PublicGroupLeaderAllocationModel

    def save(self, entity: PublicGroupLeaderAllocation):
        if entity.id is None:
            model = self._to_orm(entity)
            db.session.add(model)
            db.session.flush()
            entity.id = model.id
        else:
            model = db.session.get(self.model, entity.id, with_for_update=True)
            if model.version != entity.version:
                raise OptimisticLockError("优惠券分配数据已被其他操作更新，请重试")
            entity.version += 1
            self._update_orm(model, entity)

    def get_all(self) -> list[PublicGroupLeaderAllocation]:
        """获取所有公共团长分配"""
        result = db.session.execute(
            select(self.model)
            .order_by(self.model.created_at.desc())
        )

        return [self._from_orm(model) for model in result.scalars().all()]

    @staticmethod
    def _from_orm(model: PublicGroupLeaderAllocationModel) -> PublicGroupLeaderAllocation:
        return PublicGroupLeaderAllocation(
            id=model.id,
            coupon_allocation_id=model.coupon_allocation_id,
            total_quota=model.total_quota,
            created_by=model.created_by,
            created_at=model.created_at,
            version=model.version
        )

    @staticmethod
    def _to_orm(entity: PublicGroupLeaderAllocation) -> PublicGroupLeaderAllocationModel:
        return PublicGroupLeaderAllocationModel(
            coupon_allocation_id=entity.coupon_allocation_id,
            total_quota=entity.total_quota,
            created_by=entity.created_by,
            created_at=entity.created_at,
            version=entity.version
        )

    @staticmethod
    def _update_orm(model: PublicGroupLeaderAllocationModel, entity: PublicGroupLeaderAllocation):
        model.coupon_allocation_id = entity.coupon_allocation_id
        model.total_quota = entity.total_quota
        model.version = entity.version
