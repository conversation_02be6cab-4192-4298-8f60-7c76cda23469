# 微信支付账单管理API使用说明

## 概述

本功能实现了微信支付账单的自动下载和管理，支持交易账单和资金账单的获取。

## 功能特性

- ✅ 支持交易账单（tradebill）和资金账单（fundflowbill）下载
- ✅ 自动检查本地是否已有账单文件，避免重复下载
- ✅ 日期验证：当天10点后才能查询、3个月保留期限
- ✅ 管理员权限控制
- ✅ 完整的错误处理和日志记录

## API接口

### 1. 下载账单

**接口地址：** `POST /api/admin/bills/download`

**权限要求：** 管理员权限

**请求参数：**
```json
{
    "bill_type": "tradebill",  // 账单类型：tradebill（交易账单）或 fundflowbill（资金账单）
    "bill_date": "2024-01-01"  // 账单日期，格式：YYYY-MM-DD
}
```

**响应：**
- 成功：返回账单文件下载（.txt.gz格式）
- 失败：返回错误信息

**使用示例：**
```bash
curl -X POST "https://your-domain.com/api/admin/bills/download" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "bill_type": "tradebill",
    "bill_date": "2024-01-01"
  }' \
  --output tradebill_2024-01-01.txt.gz
```

### 2. 检查账单状态

**接口地址：** `GET /api/admin/bills/status/{bill_type}/{bill_date}`

**权限要求：** 管理员权限

**路径参数：**
- `bill_type`: 账单类型（tradebill 或 fundflowbill）
- `bill_date`: 账单日期（YYYY-MM-DD格式）

**响应示例：**
```json
{
    "code": 200,
    "data": {
        "bill_type": "tradebill",
        "bill_date": "2024-01-01",
        "exists": true,
        "file_path": "/path/to/bills/tradebill_2024-01-01.txt.gz"
    }
}
```

**使用示例：**
```bash
curl -X GET "https://your-domain.com/api/admin/bills/status/tradebill/2024-01-01" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

## 业务逻辑

### 账单下载流程

1. **参数验证**
   - 验证账单类型（tradebill/fundflowbill）
   - 验证日期格式（YYYY-MM-DD）

2. **日期有效性检查**
   - 不能查询未来日期
   - 当天账单需要在10点后查询
   - 只能查询3个月内的账单

3. **本地文件检查**
   - 检查本地是否已有该账单文件
   - 如果存在，直接返回本地文件

4. **微信支付平台下载**
   - 调用微信支付API申请账单
   - 获取下载链接
   - 下载并保存账单文件
   - 返回文件给客户端

### 文件存储

- **存储路径：** 配置项 `BILL_STORAGE_DIR`（默认：`./bills`）
- **文件命名：** `{bill_type}_{bill_date}.txt.gz`
- **示例：** `tradebill_2024-01-01.txt.gz`

## 配置说明

在 `app/config.py` 中添加了以下配置项：

```python
# 微信支付账单存储配置
BILL_STORAGE_DIR = os.getenv('BILL_STORAGE_DIR', os.path.join(os.getcwd(), 'bills'))
BILL_RETENTION_MONTHS = int(os.getenv('BILL_RETENTION_MONTHS', 3))  # 账单保留月数，默认3个月
```

### 环境变量

可以通过环境变量自定义配置：

```bash
# 账单存储目录
BILL_STORAGE_DIR=/srv/app/bills

# 账单保留月数
BILL_RETENTION_MONTHS=6
```

## 错误处理

### 常见错误码

- `400` - 参数验证失败
- `401` - 未授权（需要管理员权限）
- `404` - 账单文件未找到
- `500` - 服务器内部错误

### 错误示例

```json
{
    "code": 400,
    "message": "当天账单需要在10点后才能查询"
}
```

```json
{
    "code": 400,
    "message": "账单保留期限为3个月，无法查询该日期的账单"
}
```

## 日志记录

系统会记录以下关键操作：

- 账单申请成功/失败
- 账单下载成功/失败
- 本地文件返回
- 错误信息

日志示例：
```
[2024-01-01 10:30:00] INFO: 开始下载账单: tradebill_2024-01-01
[2024-01-01 10:30:01] INFO: 交易账单申请成功: 2024-01-01
[2024-01-01 10:30:02] INFO: 账单下载成功: tradebill_2024-01-01.txt.gz
[2024-01-01 10:30:02] INFO: 账单下载并返回成功: tradebill_2024-01-01
```

## 注意事项

1. **时间限制：** 当天账单需要在10点后才能查询
2. **保留期限：** 默认只能查询3个月内的账单
3. **文件格式：** 账单文件为gzip压缩的文本格式
4. **权限要求：** 需要管理员权限才能访问
5. **重复下载：** 系统会自动检查本地文件，避免重复下载

## 技术实现

- **微信支付SDK：** 使用 `wechatpayv3` 库
- **文件存储：** 本地文件系统
- **权限控制：** 基于JWT的管理员认证
- **参数验证：** 使用Marshmallow进行请求参数验证
