#!/usr/bin/env python3
"""
简单的账单功能测试脚本
用于验证账单下载功能的基本逻辑
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_date_validation():
    """测试日期验证逻辑"""
    print("=== 测试日期验证逻辑 ===")
    
    # 模拟WechatPaymentService的日期验证方法
    def is_bill_date_valid(bill_date: str, retention_months: int = 3) -> dict:
        try:
            # 解析日期
            target_date = datetime.strptime(bill_date, '%Y-%m-%d').date()
            today = datetime.now().date()
            
            # 检查是否是未来日期
            if target_date > today:
                return {
                    'valid': False,
                    'error': '不能查询未来日期的账单'
                }
            
            # 检查是否超过保留期限
            earliest_date = today - timedelta(days=retention_months * 30)  # 简化计算
            
            if target_date < earliest_date:
                return {
                    'valid': False,
                    'error': f'账单保留期限为{retention_months}个月，无法查询该日期的账单'
                }
            
            # 如果是当天，检查是否已过10点
            if target_date == today:
                current_hour = datetime.now().hour
                if current_hour < 10:
                    return {
                        'valid': False,
                        'error': '当天账单需要在10点后才能查询'
                    }
            
            return {'valid': True}
            
        except ValueError:
            return {
                'valid': False,
                'error': '日期格式错误，请使用YYYY-MM-DD格式'
            }
    
    # 测试用例
    test_cases = [
        ('2024-01-01', '有效的历史日期'),
        ('2025-12-31', '未来日期'),
        ('2020-01-01', '超过保留期限的日期'),
        ('invalid-date', '无效日期格式'),
        (datetime.now().strftime('%Y-%m-%d'), '今天的日期'),
        ((datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'), '一个月前的日期'),
    ]
    
    for date_str, description in test_cases:
        result = is_bill_date_valid(date_str)
        status = "✓" if result['valid'] else "✗"
        error_msg = f" - {result.get('error', '')}" if not result['valid'] else ""
        print(f"{status} {description} ({date_str}){error_msg}")

def test_file_path_generation():
    """测试文件路径生成逻辑"""
    print("\n=== 测试文件路径生成逻辑 ===")
    
    def get_bill_file_path(bill_type: str, bill_date: str, storage_dir: str = './bills') -> str:
        filename = f"{bill_type}_{bill_date}.txt.gz"
        file_path = os.path.join(storage_dir, filename)
        return file_path
    
    test_cases = [
        ('tradebill', '2024-01-01'),
        ('fundflowbill', '2024-01-01'),
        ('tradebill', '2024-12-31'),
    ]
    
    for bill_type, bill_date in test_cases:
        file_path = get_bill_file_path(bill_type, bill_date)
        print(f"✓ {bill_type} {bill_date} -> {file_path}")

def test_schema_validation():
    """测试请求参数验证逻辑"""
    print("\n=== 测试请求参数验证逻辑 ===")
    
    import re
    
    def validate_date_format(value):
        """验证日期格式是否为YYYY-MM-DD"""
        if not re.match(r'^\d{4}-\d{2}-\d{2}$', value):
            return False, '日期格式错误，请使用YYYY-MM-DD格式'
        
        try:
            datetime.strptime(value, '%Y-%m-%d')
            return True, None
        except ValueError:
            return False, '无效的日期'
    
    def validate_bill_type(value):
        """验证账单类型"""
        if value not in ['tradebill', 'fundflowbill']:
            return False, '账单类型错误，可选值：tradebill（交易账单）、fundflowbill（资金账单）'
        return True, None
    
    test_cases = [
        # (bill_type, bill_date, expected_valid)
        ('tradebill', '2024-01-01', True),
        ('fundflowbill', '2024-01-01', True),
        ('invalid_type', '2024-01-01', False),
        ('tradebill', 'invalid-date', False),
        ('tradebill', '2024-13-01', False),
        ('tradebill', '2024-01-32', False),
    ]
    
    for bill_type, bill_date, expected_valid in test_cases:
        type_valid, type_error = validate_bill_type(bill_type)
        date_valid, date_error = validate_date_format(bill_date)
        
        is_valid = type_valid and date_valid
        status = "✓" if is_valid == expected_valid else "✗"
        
        error_msgs = []
        if type_error:
            error_msgs.append(type_error)
        if date_error:
            error_msgs.append(date_error)
        
        error_str = f" - {'; '.join(error_msgs)}" if error_msgs else ""
        print(f"{status} {bill_type}, {bill_date} (期望: {'有效' if expected_valid else '无效'}){error_str}")

def test_directory_creation():
    """测试目录创建逻辑"""
    print("\n=== 测试目录创建逻辑 ===")
    
    test_dir = './test_bills'
    
    try:
        os.makedirs(test_dir, exist_ok=True)
        if os.path.exists(test_dir):
            print(f"✓ 成功创建目录: {test_dir}")
            # 清理测试目录
            os.rmdir(test_dir)
            print(f"✓ 成功清理测试目录: {test_dir}")
        else:
            print(f"✗ 目录创建失败: {test_dir}")
    except Exception as e:
        print(f"✗ 目录创建异常: {str(e)}")

if __name__ == '__main__':
    print("微信支付账单功能测试")
    print("=" * 50)
    
    test_date_validation()
    test_file_path_generation()
    test_schema_validation()
    test_directory_creation()
    
    print("\n" + "=" * 50)
    print("测试完成")
